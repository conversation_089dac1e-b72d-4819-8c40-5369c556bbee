import React from 'react';
import { generatePath } from 'react-router';
import Typography from '@mui/material/Typography';

import { User, UserId, InfoSquare, SignOut } from '@protecht/ui-library/library/components/SVGIcons';

import { LOGOUT_URL } from 'config';
import { strings } from 'common/utils/i18n';
import { VendorPortalPath } from 'vendorRiskManagement/routes/vendorPortalRoutes';
import { NavigationBarProvider } from 'ui/components/AppNavigationBar/HorizontalNavigationBar/NavigationBarContext';
import ToolbarMenu from 'ui/components/AppNavigationBar/HorizontalNavigationBar/ToolbarMenu';
import { usePursGetCurrentUserUsingGetQuery } from 'user/rtkApi';
import { BaseNavigationItem } from 'ui/components/AppNavigationBar/types';
import { localizeCustomString, localizeString, transformMenuData } from 'ui/components/AppNavigationBar/utils';
import AppBar from 'ui/components/AppNavigationBar/AppBar';
import AppLogo from 'ui/components/AppNavigationBar/AppLogo';
import { VENDOR_PORTAL_MENU_HEIGHT } from 'vendorRiskManagement/constants';

const useMenuItems = () => {
    const { data: currentUser } = usePursGetCurrentUserUsingGetQuery();

    if (!currentUser) {
        return [];
    }

    const menuItems: BaseNavigationItem[] = [
        {
            id: '__USER_ACCOUNT__',
            labels: localizeString('userAccount'),
            customIcon: User,
            items: [
                {
                    id: '__MY_ACCOUNT__',
                    titles: localizeString('userDetails'),
                    labels: localizeCustomString(currentUser.loginId!),
                    customIcon: UserId,
                    hyperlink: generatePath(VendorPortalPath.VENDOR_PORTAL_MY_ACCOUNT),
                    onClick: () => window.GwtBridge?.VendorPortal?.hideWidgets?.(),
                },
                {
                    id: '__ABOUT__',
                    labels: localizeString('aboutERM'),
                    customIcon: InfoSquare,
                    onClick: () => window.GwtBridge?.VendorPortal?.showAboutDialog?.(),
                },
                { isDivider: true, id: '_DIVIDER_LOGOUT', labels: [] },
                {
                    id: '__LOGOUT__',
                    labels: localizeString('signOut'),
                    customIcon: SignOut,
                    hyperlink: LOGOUT_URL,
                },
            ],
        },
    ];

    return transformMenuData(menuItems);
};

const Menu: React.FC = () => {
    const items = useMenuItems();

    return (
        <AppBar
            position="static"
            height={VENDOR_PORTAL_MENU_HEIGHT}
            customLogo={
                <>
                    <AppLogo logoClickable={false} />
                    <Typography
                        sx={{
                            fontSize: '26px',
                            fontWeight: 600,
                            lineHeight: '35px',
                            color: 'white',
                        }}
                    >
                        {strings('vrm:title.vendorPortal')}
                    </Typography>
                </>
            }
        >
            <NavigationBarProvider items={items}>
                <ToolbarMenu items={items} />
            </NavigationBarProvider>
        </AppBar>
    );
};

export default Menu;
