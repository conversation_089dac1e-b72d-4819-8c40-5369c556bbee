// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<PERSON>u renders dropdown when clicking on user item and contains user name 1`] = `
<div>
  <header
    class="MuiPaper-root MuiPaper-elevation MuiPaper-elevation4 MuiAppBar-root MuiAppBar-colorPrimary MuiAppBar-positionStatic modern-component css-umyg46-MuiPaper-root-MuiAppBar-root"
    data-testid="main-app-bar"
  >
    <div
      class="MuiBox-root css-1gmwkzf"
    >
      <div
        class="MuiBox-root css-18biwo"
        data-testid="app-logo"
      >
        <img
          alt="Logo"
          data-testid="app-logo-image"
          src="/camilla/worms/client/styles/style/logo_left"
          style="max-height: 40px;"
        />
      </div>
      <p
        class="MuiTypography-root MuiTypography-body1 css-gzpmqz-MuiTypography-root"
      >
        Vendor Portal
      </p>
    </div>
    <nav
      aria-label="Toolbar"
      class="css-13k28us"
      data-testid="app-menu-toolbar"
    >
      <ul
        class="css-nmii59"
      >
        <li
          aria-expanded="true"
          aria-haspopup="true"
          aria-label="User Account"
          class="MuiButtonBase-root MuiMenuItem-root MuiMenuItem-gutters MuiMenuItem-root MuiMenuItem-gutters MenuDropdownOpen MenuItemHasSubmenu css-1l7qmwu-MuiButtonBase-root-MuiMenuItem-root"
          data-testid="menu-item-__USER_ACCOUNT__"
          role="menuitem"
          tabindex="0"
        >
          <div
            class="css-10klw3m"
            data-testid="menu-item-tooltip-container"
          >
            <span
              aria-label="User Account"
              class="css-93o4tm"
              data-mui-internal-clone-element="true"
              data-testid="menu-item-tooltip"
            >
              <div
                class="MenuItemContent MuiBox-root css-1phz69o"
              >
                <div
                  class="css-7ldcpb"
                >
                  <svg
                    aria-hidden="false"
                    class="MenuItemIcon"
                    data-icon="user"
                    data-testid="item-icon"
                    fill="currentColor"
                    height="24"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 12a4.25 4.25 0 0 0 4.25-4.25A4.25 4.25 0 0 0 12 3.5a4.25 4.25 0 0 0-4.25 4.25A4.25 4.25 0 0 0 12 12m0 2.125c-2.837 0-8.5 1.424-8.5 4.25V20.5h17v-2.125c0-2.826-5.663-4.25-8.5-4.25"
                      fill="currentColor"
                    />
                  </svg>
                </div>
              </div>
            </span>
          </div>
        </li>
      </ul>
    </nav>
  </header>
</div>
`;

exports[`Menu renders menu 1`] = `
<div>
  <header
    class="MuiPaper-root MuiPaper-elevation MuiPaper-elevation4 MuiAppBar-root MuiAppBar-colorPrimary MuiAppBar-positionStatic modern-component css-umyg46-MuiPaper-root-MuiAppBar-root"
    data-testid="main-app-bar"
  >
    <div
      class="MuiBox-root css-1gmwkzf"
    >
      <div
        class="MuiBox-root css-18biwo"
        data-testid="app-logo"
      >
        <img
          alt="Logo"
          data-testid="app-logo-image"
          src="/camilla/worms/client/styles/style/logo_left"
          style="max-height: 40px;"
        />
      </div>
      <p
        class="MuiTypography-root MuiTypography-body1 css-gzpmqz-MuiTypography-root"
      >
        Vendor Portal
      </p>
    </div>
    <nav
      aria-label="Toolbar"
      class="css-13k28us"
      data-testid="app-menu-toolbar"
    >
      <ul
        class="css-nmii59"
      >
        <li
          aria-haspopup="true"
          aria-label="User Account"
          class="MuiButtonBase-root MuiMenuItem-root MuiMenuItem-gutters MuiMenuItem-root MuiMenuItem-gutters MenuItemHasSubmenu css-1l7qmwu-MuiButtonBase-root-MuiMenuItem-root"
          data-testid="menu-item-__USER_ACCOUNT__"
          role="menuitem"
          tabindex="0"
        >
          <div
            class="css-10klw3m"
            data-testid="menu-item-tooltip-container"
          >
            <span
              aria-label="User Account"
              class="css-93o4tm"
              data-mui-internal-clone-element="true"
              data-testid="menu-item-tooltip"
            >
              <div
                class="MenuItemContent MuiBox-root css-1phz69o"
              >
                <div
                  class="css-7ldcpb"
                >
                  <svg
                    aria-hidden="false"
                    class="MenuItemIcon"
                    data-icon="user"
                    data-testid="item-icon"
                    fill="currentColor"
                    height="24"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 12a4.25 4.25 0 0 0 4.25-4.25A4.25 4.25 0 0 0 12 3.5a4.25 4.25 0 0 0-4.25 4.25A4.25 4.25 0 0 0 12 12m0 2.125c-2.837 0-8.5 1.424-8.5 4.25V20.5h17v-2.125c0-2.826-5.663-4.25-8.5-4.25"
                      fill="currentColor"
                    />
                  </svg>
                </div>
              </div>
            </span>
          </div>
        </li>
      </ul>
    </nav>
  </header>
</div>
`;
