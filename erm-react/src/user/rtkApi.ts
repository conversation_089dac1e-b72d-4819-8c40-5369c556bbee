import { baseInjected<PERSON><PERSON> as baseUsersApi } from 'api/generated/users';
import { baseInjected<PERSON>pi as baseIUsersApi } from 'api/generated/iusers';
import {
    GetUsersApiArg,
    GetUsersApiResponse,
    PursiGetCurrentUserMobileQrCodeUsingGetApiArg,
    PursiGetSecurityTokenForUserUsingGetApiArg,
} from '../api/generated/types';
import { cacheProfiles } from 'api/cacheProfiles';

export const usersApiWithTags = baseUsersApi.enhanceEndpoints({
    addTagTypes: ['user', 'permissions'],
    endpoints: {
        pursGetCurrentUserUsingGet: {
            providesTags: ['user'],
        },
        pursGetUserPermissionsUsingGet: {
            providesTags: ['user', 'permissions'],
        },
        getUsers: {
            providesTags: ['user'],
        },
    },
});

export const usersApi = usersApiWithTags.injectEndpoints({
    endpoints: (builder) => ({
        getUsersSearch: builder.query<GetUsersApiResponse, GetUsersApiArg>({
            query: (queryArg) => ({
                url: '/v1/api/users/search',
                method: 'POST',
                body: queryArg.userFilterContext,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                    permissionFilterId: queryArg.permissionFilterId,
                    roleFilterId: queryArg.roleFilterId,
                },
            }),
        }),
        // Injected so responseHandler is correct
        // TODO: figure out how to use the correct responseHandler in the generated endpoints
        getCurrentUserMobileQrCode: builder.query<string, PursiGetCurrentUserMobileQrCodeUsingGetApiArg>({
            query: (queryArg) => ({
                url: '/v1/api/users/me/mobile/qrcode',
                params: {
                    size: queryArg.size,
                },
                responseHandler: 'text',
                ...cacheProfiles.staticData,
            }),
        }),
        // Injected so responseHandler is correct
        // TODO: figure out how to use the correct responseHandler in the generated endpoints
        getSecurityTokenForUser: builder.query<string, PursiGetSecurityTokenForUserUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/users/${queryArg.userId}/security/token`,
                responseHandler: 'text',
                ...cacheProfiles.staticData,
            }),
        }),
    }),
});

export const {
    usePursGetCurrentUserUsingGetQuery,
    useLazyPursGetCurrentUserUsingGetQuery,
    usePursUpdateUserUsingPutMutation,
    useLazyGetUsersQuery,
    useGetUsersQuery,
    usePursGetUserUsingGetQuery,
    useLazyPursGetUsersUsingGetQuery,
    useLazyGetUsersSearchQuery,
    usePursGetUserPermissionsUsingGetQuery,
    useGetCurrentUserMobileQrCodeQuery,
    useLazyGetSecurityTokenForUserQuery,
} = usersApi;

export const iusersApiWithTags = baseIUsersApi.enhanceEndpoints({
    addTagTypes: ['iuser'],
    endpoints: {
        pursiGetCurrentUserInternalUsingGet: {
            providesTags: ['iuser'],
        },
        pursiUpdateCurrentUserUsingPut: {
            invalidatesTags: ['iuser'],
        },
    },
});

export const { usePursiGetCurrentUserInternalUsingGetQuery, usePursiUpdateCurrentUserUsingPutMutation, usePursiGetUserDashboardsUsingGetQuery } =
    iusersApiWithTags;
