import { DATA_SYSTEM_COLUMN, DataGridColDef, SYSTEM_COLUMN } from 'common/types';
import {
    createIdColumnDef,
    createNameColumnDef,
    createDescriptionColumnDef,
    createTagsColumnDef,
    createCreateDateColumnDef,
    createCreatedByColumnDef,
    createLastModifiedDateColumnDef,
    createLastModifiedByColumnDef,
} from '../shared/CommonColumnDefinitions';

export const DEFAULT_ORDER_FIELD = SYSTEM_COLUMN.ID;
export const DEFAULT_SEARCH_FIELD = 'name';

export const RiskCauseColDef: DataGridColDef[] = [
    createIdColumnDef(),
    createNameColumnDef(),
    createDescriptionColumnDef(true, 'note'),
    createTagsColumnDef(),
    { ...createCreateDateColumnDef(), apiField: DATA_SYSTEM_COLUMN.FIELD_CREATED_DATE },
    createCreatedByColumnDef(),
    createLastModifiedDateColumnDef(),
    createLastModifiedByColumnDef(),
];
