import { baseInjected<PERSON>pi } from 'api/generated/risk';
import { GetRiskCausesApiArg, GetRiskCausesApiResponse } from 'api/generated/types';

export const riskCauseApiWithTags = baseInjectedApi.enhanceEndpoints({
    addTagTypes: ['riskCauses'],
    endpoints: {
        rcrsGetRiskCauseUsingGet: {
            providesTags: (result, _error, arg) => (result ? [{ type: 'riskCauses', id: arg.riskCauseId }] : []),
        },
    },
});

const riskCauseApi = riskCauseApiWithTags.injectEndpoints({
    endpoints: (builder) => ({
        getRiskCausesSearch: builder.query<GetRiskCausesApiResponse, GetRiskCausesApiArg>({
            query: (queryArg) => ({
                url: '/v1/api/risk/causes/search',
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    tagType: queryArg.tagType,
                    tagOperator: queryArg.tagOperator,
                    tagIds: queryArg.tagIds,
                    viewId: queryArg.viewId,
                    statuses: queryArg.statuses,
                },
            }),
            providesTags: (result, _error, _arg) => (result ? [{ type: 'riskCauses', id: 'list' }] : []),
        }),
    }),
});

export const { useGetRiskCausesSearchQuery, useRcrsGetRiskCauseUsingGetQuery, usePrefetch, useLazyGetRiskCausesSearchQuery } = riskCauseApi;
