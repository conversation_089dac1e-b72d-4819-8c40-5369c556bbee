import { ViewExpressionRest } from 'api/generated/types';
import { useGetRiskCausesSearchQuery } from './rtkApi';
import useLibraryEntriesLoaderState from 'library/hooks/useLibraryEntriesLoaderState';
import { getLibraryLoaderState } from 'library/utils';

export const useRiskCauseLoader = (additionalExpressions?: ViewExpressionRest[]) => {
    const { queryArgs, enabled, handleParamsChange } = useLibraryEntriesLoaderState(additionalExpressions);

    const queryResult = useGetRiskCausesSearchQuery(queryArgs!, {
        skip: !enabled || !queryArgs,
    });

    return getLibraryLoaderState(queryResult, handleParamsChange);
};

export default useRiskCauseLoader;
