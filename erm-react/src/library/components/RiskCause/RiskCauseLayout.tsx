import React from 'react';
import { IdOnly } from 'app/types';
import { ViewContextType } from 'view/types';
import { DEFAULT_ORDER_FIELD, DEFAULT_SEARCH_FIELD, RiskCauseColDef } from './RiskCauseDefinitions';
import { TagContext } from 'common/types';
import { ViewExpressionRest } from 'api/generated/types';
import LibraryEntriesWithTagFilter from '../shared/LibraryEntriesWithTagFilter';
import { strings } from 'common/utils/i18n';
import useRiskCauseLoader from './useRiskCauseLoader';
import { RiskCause } from './types';

type Props = {
    additionalExpressions?: ViewExpressionRest[];
    isSelector?: boolean;
    multiselect?: boolean;
    persistViewState?: boolean;
    selected?: IdOnly[];
    onSelect?: (currentSelection: IdOnly[]) => void;
    onLoadingChange?: (isLoading: boolean) => void;
};

const RiskCauseLayout: React.FC<Props> = ({
    additionalExpressions,
    isSelector = false,
    multiselect = false,
    selected = [],
    persistViewState = true,
    onSelect,
    onLoadingChange,
}: Props) => {
    const { handleParamsChange, ...loaderResult } = useRiskCauseLoader(additionalExpressions);

    return (
        <LibraryEntriesWithTagFilter<RiskCause>
            colDef={RiskCauseColDef}
            defaultOrderField={DEFAULT_ORDER_FIELD}
            defaultSearchField={DEFAULT_SEARCH_FIELD}
            loaderResult={loaderResult}
            onParamsChange={handleParamsChange}
            isSelector={isSelector}
            multiselect={multiselect}
            tagContext={TagContext.RISK_CAUSE}
            title={strings('ermConstants:risk_causes')}
            viewContext={ViewContextType.RiskCauses}
            selected={selected}
            persistViewState={persistViewState}
            onSelect={onSelect}
            onLoadingChange={onLoadingChange}
        />
    );
};

export default RiskCauseLayout;
