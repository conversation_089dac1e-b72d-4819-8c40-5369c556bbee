import { ViewExpressionRest } from 'api/generated/types';
import { useGetUsersQuery } from 'user/rtkApi';
import useLibraryEntriesLoaderState from 'library/hooks/useLibraryEntriesLoaderState';
import { useMemo } from 'react';
import { getLibraryLoaderState } from 'library/utils';

export const useUserLoader = (additionalExpressions?: ViewExpressionRest[]) => {
    const { queryArgs, enabled, handleParamsChange } = useLibraryEntriesLoaderState(additionalExpressions);

    const userFilterContext = useMemo(() => {
        return { expressions: [...(queryArgs?.filterContextRest.expressions ?? [])] };
    }, [queryArgs?.filterContextRest.expressions]);

    const queryResult = useGetUsersQuery({ ...queryArgs, userFilterContext }!, {
        skip: !enabled || !queryArgs,
    });

    return getLibraryLoaderState(queryResult, handleParamsChange);
};

export default useUserLoader;
