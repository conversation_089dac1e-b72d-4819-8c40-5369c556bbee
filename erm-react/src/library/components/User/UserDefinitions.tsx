import React from 'react';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { GridValueFormatterParams } from '@mui/x-data-grid-pro';
import { DataGridColDef } from 'common/types';
import { MIN_TABLE_COLUMN_WIDTH } from 'common/constants';
import { FilterType } from 'view/types';
import { IdWithName, ObjectStatus } from 'app/types';
import { strings } from 'common/utils/i18n';
import { dateFormatter, getDateWithFormat } from 'common/utils/dateUtils';
import { getCurrentUser } from 'app/selectors';
import store from 'store';
import { CellRenderDefault } from 'common/components/Table/Cell/CellWithTooltip';
import { createIdColumnDef } from '../shared/CommonColumnDefinitions';

export const DEFAULT_SEARCH_FIELD = 'name';
export const DEFAULT_ORDER_FIELD = 'name';

export const UserColDef: DataGridColDef[] = [
    createIdColumnDef(),
    {
        field: 'name',
        headerName: strings('user:label.name'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        renderCell: (params) => {
            return (
                <Grid
                    container
                    flexWrap="nowrap"
                    spacing={1}
                >
                    <Grid
                        item
                        overflow="hidden"
                    >
                        <CellRenderDefault
                            value={params.value}
                            formattedValue={params.formattedValue}
                            computedCellWidth={params.colDef.computedWidth}
                        ></CellRenderDefault>
                    </Grid>
                    {params.row.status === ObjectStatus.Disabled && (
                        <Grid item>
                            <Typography
                                variant="body1"
                                color="accentColors.analyticsOrange"
                            >
                                {strings('ermConstants:status_locked')}
                            </Typography>
                        </Grid>
                    )}
                </Grid>
            );
        },
    },
    {
        field: 'loginId',
        apiField: 'loginid',
        headerName: strings('user:label.username'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'userType',
        headerName: strings('user:label.userType'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        apiField: 'positionheld',
        field: 'position',
        headerName: strings('user:label.position'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'employeeId',
        apiField: 'employeeid',
        headerName: strings('user:label.employeeId'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'email',
        headerName: strings('user:label.email'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'contactNumber',
        headerName: strings('user:label.phone'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'manager',
        headerName: strings('user:label.manager'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        valueFormatter: (params: GridValueFormatterParams) => (params.value as IdWithName)?.name,
        flex: 1,
    },
    {
        apiField: 'lastlogindate',
        field: 'lastLoginDate',
        headerName: strings('user:label.lastLogin'),
        filterType: FilterType.DATE,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueGetter: (params) => getDateWithFormat(params),
        valueFormatter: ({ value }) => dateFormatter(value, getCurrentUser(store.getState())),
    },
    // todo finish, missing in response
    // {
    //     field: 'lastPasswordChange',
    //     headerName: strings('user:label.lastPasswordChange'),
    // },
    {
        field: 'businessUnit',
        headerName: strings('user:label.businessUnit'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        valueFormatter: (params: GridValueFormatterParams) => (params.value as IdWithName)?.name,
        flex: 1,
    },
    {
        field: 'mobile',
        headerName: strings('user:label.mobile'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'instantMessenger',
        headerName: strings('user:label.instantMessenger'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'categoryVector',
        filterType: FilterType.STRING,
        headerName: strings('user:label.categoryVector'),
        valueFormatter: (params: GridValueFormatterParams) => {
            return params.value === 1 ? strings('user:label.internal') : strings('user:label.vendor');
        },
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    // todo finish missing in response
    // {
    //     field: 'createdDate',
    //     headerName: strings('user:label.createdDate'),
    //     // filterType: FilterType.STRING,
    //     // minWidth: MIN_TABLE_COLUMN_WIDTH,
    //     flex: 1,
    // },
    // {
    //     apiField: 'createdBy.name',
    //     field: 'createdBy.name',
    //     headerName: strings('user:label.createdBy'),
    //     // filterType: FilterType.STRING,
    //     // minWidth: MIN_TABLE_COLUMN_WIDTH,
    //     flex: 1,
    // },
    // {
    //     field: 'lastModifiedDate',
    //     headerName: strings('user:label.lastModifiedDate'),
    //     // filterType: FilterType.STRING,
    //     // minWidth: MIN_TABLE_COLUMN_WIDTH,
    //     flex: 1,
    // },
    // {
    //     field: 'lastModifiedBy.name',
    //     apiField: 'lastModifiedBy.name',
    //     headerName: strings('user:label.lastModifiedBy'),
    //     // filterType: FilterType.STRING,
    //     // minWidth: MIN_TABLE_COLUMN_WIDTH,
    //     flex: 1,
    // },
];

export const LibrarySelectorEntriesColDef: DataGridColDef[] = [
    {
        field: 'name',
        headerName: strings('user:label.name'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        renderCell: (params) => {
            return (
                <Grid
                    container
                    flexWrap="nowrap"
                    spacing={1}
                >
                    <Grid
                        item
                        overflow="hidden"
                    >
                        <CellRenderDefault
                            value={params.value}
                            formattedValue={params.formattedValue}
                            computedCellWidth={params.colDef.computedWidth}
                        ></CellRenderDefault>
                    </Grid>
                    {params.row.status === ObjectStatus.Disabled && (
                        <Grid item>
                            <Typography
                                variant="body1"
                                color="accentColors.analyticsOrange"
                            >
                                {strings('ermConstants:status_locked')}
                            </Typography>
                        </Grid>
                    )}
                </Grid>
            );
        },
    },
    {
        field: 'loginId',
        apiField: 'loginid',
        headerName: strings('user:label.username'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'userType',
        headerName: strings('user:label.userType'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        apiField: 'positionheld',
        field: 'position',
        headerName: strings('user:label.position'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'employeeId',
        apiField: 'employeeid',
        headerName: strings('user:label.employeeId'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'businessUnit',
        headerName: strings('user:label.businessUnit'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        valueFormatter: (params: GridValueFormatterParams) => (params.value as IdWithName)?.name,
        flex: 1,
    },
    {
        field: 'email',
        headerName: strings('user:label.email'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'contactNumber',
        headerName: strings('user:label.phone'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'mobile',
        headerName: strings('user:label.mobile'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'manager',
        headerName: strings('user:label.manager'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        valueFormatter: (params: GridValueFormatterParams) => (params.value as IdWithName)?.name,
        flex: 1,
    },
    {
        apiField: 'lastlogindate',
        field: 'lastLoginDate',
        headerName: strings('user:label.lastLogin'),
        filterType: FilterType.DATE,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueGetter: (params) => getDateWithFormat(params),
        valueFormatter: ({ value }) => dateFormatter(value, getCurrentUser(store.getState())),
    },
    // todo finish, missing in response
    // {
    //     field: 'lastPasswordChange',
    //     headerName: strings('user:label.lastPasswordChange'),
    // },
    {
        field: 'instantMessenger',
        headerName: strings('user:label.instantMessenger'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    // todo finish, missing in response
    // two factor authentication
    // email digest
    // user category
    {
        field: 'categoryVector',
        filterType: FilterType.STRING,
        headerName: strings('user:label.categoryVector'),
        valueFormatter: (params: GridValueFormatterParams) => {
            return params.value === 1 ? strings('user:label.internal') : strings('user:label.vendor');
        },
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
];
