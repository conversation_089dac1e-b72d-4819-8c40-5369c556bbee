import React from 'react';

import { GridValueFormatterParams } from '@mui/x-data-grid';

import { DataGridColDef, FilterType } from '@protecht/ui-library/library/types';

import CheckBoxCell from 'common/components/Table/Cell/CheckBoxCell';
import { ColorCell } from 'common/components/Table/Cell/RichCellWithTooltip';
import { dateFormatter } from 'common/utils/dateUtils';
import { getCurrentUser } from 'app/selectors';
import { MIN_TABLE_COLUMN_WIDTH } from 'common/constants';
import store from 'store';
import { strings } from 'common/utils/i18n';
import Typography from '@mui/material/Typography';

const CONTROL_STYLES = {
    CONTROL_1: { color: '#000000', backgroundColor: '#CCFF99' },
    CONTROL_2: { color: '#ffffff', backgroundColor: '#ff0033' },
    QUESTION_1: { color: '#000000', backgroundColor: '#CCFF99' },
    QUESTION_2: { color: '#ffffff', backgroundColor: '#ff0033' },
    QUESTION_3: { color: '#000000', backgroundColor: '#EEEEEE' },
};

export const ComplianceColDef: DataGridColDef[] = [
    {
        field: 'control',
        apiField: 'controlName',
        headerName: strings('compliance:label.controlName'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params: GridValueFormatterParams) => params.value?.name ?? '',
    },
    {
        field: 'actions',
        apiField: 'actionCount',
        headerName: strings('compliance:label.actions'),
        filterType: FilterType.NUMBER,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params: GridValueFormatterParams) => params.value?.length ?? '',
    },
    {
        field: 'riskName',
        headerName: strings('compliance:label.riskName'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'businessUnit',
        apiField: 'businessUnitName',
        headerName: strings('compliance:label.businessUnit'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params: GridValueFormatterParams) => params.value?.name ?? '',
    },
    {
        field: 'controlDescription',
        headerName: strings('compliance:label.controlDescription'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'category',
        headerName: strings('compliance:label.category'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'changedAfterCutOff',
        headerName: strings('compliance:label.changedAfterCutOff'),
        filterType: FilterType.BOOLEAN,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        renderCell: (params) => (
            <CheckBoxCell
                value={params.value}
                formattedValue={params.formattedValue}
                computedCellWidth={params.colDef.computedWidth}
            />
        ),
    },
    {
        field: 'confirmed',
        headerName: strings('compliance:label.confirmed'),
        filterType: FilterType.BOOLEAN,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        renderCell: (params) => (
            <CheckBoxCell
                value={params.value}
                formattedValue={params.formattedValue}
                computedCellWidth={params.colDef.computedWidth}
            />
        ),
    },
    {
        field: 'createDate',
        headerName: strings('compliance:label.createDate'),
        filterType: FilterType.DATE,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params) => dateFormatter(params, getCurrentUser(store.getState()), true),
    },
    {
        field: 'lastModifiedDate',
        headerName: strings('compliance:label.lastModifiedDate'),
        filterType: FilterType.DATE,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params) => dateFormatter(params, getCurrentUser(store.getState()), true),
    },
    {
        field: 'comment',
        headerName: strings('compliance:label.comment'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'status',
        headerName: strings('compliance:label.status'),
        filterType: FilterType.NUMBER,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'controlCompleted',
        headerName: strings('compliance:label.controlCompleted'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueGetter: (params) => {
            const controlCompleted = params.row.controlCompleted;
            return controlCompleted ? params.row.response : '';
        },
        renderCell: (params) => {
            const status = params.row.controlCompleted;
            const controlType = params.row.controlType;

            let styles: { backgroundColor?: string; color?: string } = {};

            if (status !== undefined || controlType !== undefined) {
                styles = CONTROL_STYLES[controlType + '_' + status];
            }

            return (
                <ColorCell
                    value={params.value}
                    formattedValue={params.formattedValue}
                    computedCellWidth={params.colDef.computedWidth}
                    bgColor={params.formattedValue ? styles.backgroundColor : undefined}
                    textColor={params.formattedValue ? styles.color : undefined}
                    label={
                        params.formattedValue || (
                            <Typography
                                variant="body2"
                                color="primary.main"
                                width="100%"
                                textAlign="center"
                            >
                                {strings('compliance:label.fillResponse')}
                            </Typography>
                        )
                    }
                ></ColorCell>
            );
        },
    },
    {
        field: 'endOfPeriod',
        headerName: strings('compliance:label.endOfPeriod'),
        filterType: FilterType.DATE,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params) => dateFormatter(params, getCurrentUser(store.getState()), true),
    },
    {
        field: 'user',
        apiField: 'userName',
        headerName: strings('compliance:label.userName'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params) => params.value?.name ?? '',
    },
    {
        field: 'attestationFrequency',
        headerName: strings('compliance:label.attestationFrequency'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'controlFrequency',
        headerName: strings('compliance:label.controlFrequency'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'controlType',
        headerName: strings('compliance:label.controlType'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    // todo missing in response
    // {
    //     field: 'tagsAsString',
    //     headerName: strings('kri:label.tagsAsString'),
    //     filterType: FilterType.STRING,
    //     minWidth: MIN_TABLE_COLUMN_WIDTH,
    //     flex: 1,
    // },
];
