import { ViewExpressionRest } from 'api/generated/types';
import { useGetKrisSearchQuery } from './rtkApi';
import useLibraryEntriesLoaderState from 'library/hooks/useLibraryEntriesLoaderState';
import { getLibraryLoaderState } from 'library/utils';

export const useKriLoader = (additionalExpressions?: ViewExpressionRest[]) => {
    const { queryArgs, enabled, handleParamsChange } = useLibraryEntriesLoaderState(additionalExpressions);

    const queryResult = useGetKrisSearchQuery(queryArgs!, {
        skip: !enabled || !queryArgs,
    });

    return getLibraryLoaderState(queryResult, handleParamsChange);
};

export default useKriLoader;
