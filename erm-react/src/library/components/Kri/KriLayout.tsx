import React from 'react';

import { IdOnly } from 'app/types';
import { ViewContextType } from 'view/types';
import { KriLibrarySelectorEntriesColDef, DEFAULT_ORDER_FIELD, DEFAULT_SEARCH_FIELD } from './KriDefinitions';
import { TagContext } from 'common/types';
import LibraryEntriesWithTagFilter from '../shared/LibraryEntriesWithTagFilter';
import { ViewExpressionRest } from 'api/generated/types';
import { strings } from 'common/utils/i18n';
import useKriLoader from './useKriLoader';
import { Kri } from './types';

type Props = {
    additionalExpressions?: ViewExpressionRest[];
    isSelector?: boolean;
    multiselect?: boolean;
    persistViewState?: boolean;
    selected?: IdOnly[];
    onSelect?: (currentSelection: IdOnly[]) => void;
    onLoadingChange?: (isLoading: boolean) => void;
};

const KriLayout: React.FC<Props> = ({
    additionalExpressions,
    isSelector = false,
    multiselect = false,
    persistViewState = true,
    selected = [],
    onSelect,
    onLoadingChange,
}: Props) => {
    const { handleParamsChange, ...loaderResult } = useKriLoader(additionalExpressions);

    return (
        <LibraryEntriesWithTagFilter<Kri>
            colDef={KriLibrarySelectorEntriesColDef}
            defaultOrderField={DEFAULT_ORDER_FIELD}
            defaultSearchField={DEFAULT_SEARCH_FIELD}
            isSelector={isSelector}
            loaderResult={loaderResult}
            multiselect={multiselect}
            tagContext={TagContext.KRI}
            title={strings('kri:label.keyRiskIndicators')}
            viewContext={ViewContextType.KriLibrary}
            selected={selected}
            persistViewState={persistViewState}
            onParamsChange={handleParamsChange}
            onSelect={onSelect}
            onLoadingChange={onLoadingChange}
        />
    );
};

export default KriLayout;
