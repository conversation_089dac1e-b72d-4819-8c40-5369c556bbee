import { baseInjected<PERSON>pi as krisApi } from 'api/generated/kris';
import { KrirsGetKrisUsingPostApiArg, KrirsGetKrisUsingPostApiResponse } from 'api/generated/types';

export const kriApiWithTags = krisApi.enhanceEndpoints({
    addTagTypes: ['kriEntries', 'kriLibrarySelectorEntries'],
    endpoints: {
        kriersGetAllKeyRiskIndicatorEntriesUsingPost: {
            invalidatesTags: ['kriEntries'],
        },
        krirsGetRiskLibraryUsingGet: {
            providesTags: (result, _error, arg) => (result ? [{ type: 'kriLibrarySelectorEntries', id: arg.kriLibraryId }] : []),
        },
    },
});

const kriApi = kriApiWithTags.injectEndpoints({
    endpoints: (builder) => ({
        getKrisSearch: builder.query<KrirsGetKrisUsingPostApiResponse, KrirsGetKrisUsingPostApiArg>({
            query: (queryArg) => ({
                url: '/v1/api/kris/search',
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    tagType: queryArg.tagType,
                    tagOperator: queryArg.tagOperator,
                    tagIds: queryArg.tagIds,
                    viewId: queryArg.viewId,
                },
            }),
            providesTags: (result, _error, _arg) => (result ? [{ type: 'kriLibrarySelectorEntries', id: 'list' }] : []),
        }),
    }),
});

export const { useKriersGetAllKeyRiskIndicatorEntriesUsingPostMutation, useGetKrisSearchQuery, useKrirsGetRiskLibraryUsingGetQuery, usePrefetch } = kriApi;
