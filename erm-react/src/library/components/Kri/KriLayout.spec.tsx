import { render, screen } from 'test/utils/rtl';
import <PERSON><PERSON><PERSON>ayout from './KriLayout';
import React from 'react';
import { waitFor } from '@testing-library/react';
import { mockedTagTree } from 'common/components/TagFilter/mocks';
import { ViewRest } from 'api/generated/types';
import { strings } from 'common/utils/i18n';

const mockedDescription = '<b style="color: rgb(38,43,49);">test description</b>';

jest.mock('./rtkApi', () => {
    return {
        ...jest.requireActual('./rtkApi'),
        useGetKrisSearchQuery: jest.fn(() => ({
            data: {
                records: [{ id: 1, name: 'Test Kri', description: mockedDescription }],
                totalCount: 1,
            },
            isFetching: false,
            isSuccess: true,
        })),
    };
});

const viewData = {
    views: [],
} as ViewRest;

jest.mock('view/rtkApi', () => ({
    ...jest.requireActual('view/rtkApi'),
    useVrsGetViewsUsingGetQuery: jest.fn(() => ({
        data: viewData,
        isLoading: false,
        isSuccess: true,
        isError: false,
        refetch: jest.fn(),
    })),
}));

jest.mock('library/rtkApi', () => ({
    ...jest.requireActual('library/rtkApi'),
    useTrsGetTagTreeUsingGetQuery: jest.fn(() => ({
        data: mockedTagTree,
        isLoading: false,
        isSuccess: true,
        isError: false,
    })),
}));

describe('KriLayout', () => {
    it('renders KriLayout', async () => {
        render(<KriLayout />);

        expect(screen.getByPlaceholderText(/search/i)).toBeInTheDocument();

        expect(screen.getByTestId('button-view-selector')).toBeInTheDocument();

        await waitFor(() => {
            expect(screen.getByText('Test Kri')).toBeInTheDocument();
        });
    });

    it('displays tag filter on button click', async () => {
        const { user } = render(<KriLayout />);

        const openTagFilterButton = screen.getByTestId('openTagFilter');
        expect(openTagFilterButton).toBeInTheDocument();

        await user.click(openTagFilterButton);

        const tagFilterTitle = screen.getByText(strings('common:label.filterByTag'));

        expect(tagFilterTitle).toBeInTheDocument();
    });

    it('Hides view selector when tag filter is opened', async () => {
        const { user } = render(<KriLayout />);

        const viewSelector = screen.getByTestId('button-view-selector');
        expect(viewSelector).toBeInTheDocument();

        const openTagFilterButton = screen.getByTestId('openTagFilter');
        expect(openTagFilterButton).toBeInTheDocument();

        await user.click(openTagFilterButton);

        const tagFilterTitle = screen.getByText(strings('common:label.filterByTag'));
        expect(tagFilterTitle).toBeInTheDocument();

        await waitFor(() => {
            expect(viewSelector).not.toBeVisible();
        });
    });

    it('Hides search field selector when tag filter is opened', async () => {
        const { user } = render(<KriLayout />);

        const fieldSelector = screen.getByTestId('fieldSelector');
        expect(fieldSelector).toBeInTheDocument();

        const openTagFilterButton = screen.getByTestId('openTagFilter');
        expect(openTagFilterButton).toBeInTheDocument();

        await user.click(openTagFilterButton);

        const tagFilterTitle = screen.getByText(strings('common:label.filterByTag'));
        expect(tagFilterTitle).toBeInTheDocument();

        await waitFor(() => {
            expect(fieldSelector).not.toBeInTheDocument();
        });
    });

    it('renders description column with html content as styled text', async () => {
        render(<KriLayout />);

        await waitFor(() => {
            expect(screen.getByText('test description')).toBeInTheDocument();
        });
        expect(screen.queryByText(mockedDescription)).not.toBeInTheDocument();
    });
});
