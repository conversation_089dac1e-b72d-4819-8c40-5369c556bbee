import React from 'react';

import { GridValueFormatterParams } from '@mui/x-data-grid';
import Typography from '@mui/material/Typography';

import { DataGridColDef, FilterType, SYSTEM_COLUMN } from '@protecht/ui-library/library/types';

import CheckBoxCell from 'common/components/Table/Cell/CheckBoxCell';
import { ColorCell } from 'common/components/Table/Cell/RichCellWithTooltip';
import { dateFormatter } from 'common/utils/dateUtils';
import { getCurrentUser } from 'app/selectors';
import { MIN_TABLE_COLUMN_WIDTH } from 'common/constants';
import store from 'store';
import { strings } from 'common/utils/i18n';
import { createIdColumnDef, createDescriptionColumnDef, createTagsColumnDef, createNameColumnDef } from '../shared/CommonColumnDefinitions';

export const DEFAULT_ORDER_FIELD = SYSTEM_COLUMN.ID;
export const DEFAULT_SEARCH_FIELD = 'name';

export const KriColDef: DataGridColDef[] = [
    createIdColumnDef(),
    createNameColumnDef(),
    {
        field: 'status',
        headerName: strings('kri:label.status'),
        filterType: FilterType.NUMBER,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        renderCell: (params) => {
            return params.value === 4 ? (
                <ColorCell
                    value={params.value}
                    formattedValue={params.formattedValue}
                    computedCellWidth={params.colDef.computedWidth}
                    bgColor="#FF0033"
                    textColor="#FFFFFF"
                    label={strings('common:label.error')}
                ></ColorCell>
            ) : (
                ''
            );
        },
    },
    {
        field: 'actions',
        apiField: 'actionCount',
        headerName: strings('kri:label.actions'),
        filterType: FilterType.NUMBER,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params: GridValueFormatterParams) => params.value?.length ?? '',
    },
    {
        field: 'businessUnit',
        apiField: 'businessUnitName',
        headerName: strings('kri:label.businessUnit'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params: GridValueFormatterParams) => params.value?.name ?? '',
    },
    {
        field: 'completionBusinessUnit',
        apiField: 'completionBusinessUnitName',
        headerName: strings('kri:label.completionBusinessUnitName'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params: GridValueFormatterParams) => params.value?.name ?? '',
    },
    {
        field: 'completionRole',
        apiField: 'completionRoleName',
        headerName: strings('kri:label.completionRoleName'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params: GridValueFormatterParams) => params.value?.name ?? '',
    },
    // todo missing in response
    // {
    //     field: 'completionUser',
    //     apiField: 'completionUserName',
    //     headerName: strings('kri:label.completionUserName'),
    //     filterType: FilterType.STRING,
    //     minWidth: MIN_TABLE_COLUMN_WIDTH,
    //     flex: 1,
    //     valueFormatter: (params: GridValueFormatterParams) => params.value?.name ?? '',
    // },
    {
        // duplicate column for value displayed as qualitative value, derived from value field, not used for sorting
        // when used in expressions, qualitativeValue property is changed to value property
        field: 'qualitativeValue',
        apiField: 'qualitativeValue',
        headerName: strings('kri:label.qualitativeValue'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        sortable: false,
        flex: 1,
        valueGetter: (params) => {
            const qualitativeLabel = params.row.qualitativeLabel;
            return qualitativeLabel ? params.row.value : '';
        },
    },
    {
        field: 'value',
        headerName: strings('kri:label.value'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueGetter: (params) => {
            const qualitativeLabel = params.row.qualitativeLabel;
            return qualitativeLabel ?? params.value ?? '';
        },
        renderCell: (params) => {
            return (
                <ColorCell
                    value={params.value}
                    formattedValue={params.formattedValue}
                    computedCellWidth={params.colDef.computedWidth}
                    bgColor={params.formattedValue ? params.row.bgColor : undefined}
                    textColor={params.formattedValue ? params.row.color : undefined}
                    label={
                        params.formattedValue || (
                            <Typography
                                variant="body2"
                                color="primary.main"
                                width="100%"
                                textAlign="center"
                            >
                                {strings('kri:label.fillValue')}
                            </Typography>
                        )
                    }
                ></ColorCell>
            );
        },
    },
    {
        field: 'endOfPeriod',
        headerName: strings('kri:label.endOfPeriod'),
        filterType: FilterType.DATE,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params) => {
            const date = dateFormatter(params, getCurrentUser(store.getState()), true);
            return date;
        },
    },
    {
        field: 'type',
        headerName: strings('kri:label.type'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'keyRiskIndicator',
        headerName: strings('kri:label.keyRiskIndicator'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params: GridValueFormatterParams) => params.value?.name ?? '',
    },
    {
        field: 'bgColor',
        headerName: strings('kri:label.color'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params: GridValueFormatterParams) => {
            const color = params.value;
            const GREEN_COLOR = '#CCFF99';
            const AMBER_COLOR = '#FFCC66';
            const RED_COLOR = '#FF0033';

            if (GREEN_COLOR.toLowerCase() === color.toLowerCase()) {
                return strings('kri:label.green');
            } else if (AMBER_COLOR.toLowerCase() === color.toLowerCase()) {
                return strings('kri:label.amber');
            } else if (RED_COLOR.toLowerCase() === color.toLowerCase()) {
                return strings('kri:label.red');
            } else {
                return '';
            }
        },
    },
    {
        field: 'comment',
        headerName: strings('kri:label.comment'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    createDescriptionColumnDef(false, undefined),
    {
        field: 'frequency',
        headerName: strings('kri:label.frequency'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'riskName',
        headerName: strings('kri:label.riskName'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'scaled',
        headerName: strings('kri:label.scaled'),
        filterType: FilterType.NUMBER,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params: GridValueFormatterParams) => (params.value !== undefined ? Math.round(params.value) : ''),
    },
    {
        field: 'weight',
        headerName: strings('kri:label.weight'),
        filterType: FilterType.NUMBER,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    // todo missing in response
    // {
    //     field: 'tagsAsString',
    //     headerName: strings('kri:label.tagsAsString'),
    //     filterType: FilterType.STRING,
    //     minWidth: MIN_TABLE_COLUMN_WIDTH,
    //     flex: 1,
    // },
    {
        field: 'confirmed',
        headerName: strings('kri:label.confirmed'),
        filterType: FilterType.BOOLEAN,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        renderCell: (params) => (
            <CheckBoxCell
                value={params.value}
                formattedValue={params.formattedValue}
                computedCellWidth={params.colDef.computedWidth}
            />
        ),
    },
];

export const KriLibrarySelectorEntriesColDef = [
    createNameColumnDef(),
    createDescriptionColumnDef(true, 'note'),
    {
        field: 'kriCategory',
        headerName: strings('kri:label.category'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params: GridValueFormatterParams) => {
            return params.value?.name;
        },
    },
    {
        field: 'riskEvents',
        headerName: strings('kri:label.riskEvents'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params: GridValueFormatterParams) => {
            return params.value?.map((riskEvent) => riskEvent.name).join(', ');
        },
    },
    createTagsColumnDef(),
];
