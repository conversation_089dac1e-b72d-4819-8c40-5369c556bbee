import React from 'react';

import { strings } from 'common/utils/i18n';
import { DataGridColDef } from 'common/types';
import { MIN_TABLE_COLUMN_WIDTH } from 'common/constants';
import { FilterType } from 'view/types';
import { CellRenderDefault } from 'common/components/Table/Cell/CellWithTooltip';
import { createDescriptionColumnDef, createIdColumnDef, createNameColumnDef } from '../shared/CommonColumnDefinitions';

export const DEFAULT_ORDER_FIELD = 'description';
export const DEFAULT_SEARCH_FIELD = 'name';

export const RoleColDef: DataGridColDef[] = [
    createIdColumnDef(),
    createNameColumnDef(),
    {
        field: 'userType',
        headerName: strings('rolesAndPermissions:label.userType'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    createDescriptionColumnDef(),
    {
        field: 'homepage',
        apiField: 'homepageDashboard',
        headerName: strings('rolesAndPermissions:label.homepage'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        filterable: false,
        groupable: false,
        sortable: false,
        valueGetter: (params) => {
            if (params.row.dashboard) {
                return `${strings('rolesAndPermissions:label.dashboard')} - ${params.row.dashboard}`;
            }

            if (params.value) {
                return strings(`rolesAndPermissions:label.homepageType.${params.value}`);
            }

            return '';
        },
        renderCell: (params) => (
            <CellRenderDefault
                value={params.value}
                formattedValue={params.formattedValue}
                computedCellWidth={params.colDef.computedWidth}
            />
        ),
    },
    {
        field: 'permissionCount',
        headerName: strings('rolesAndPermissions:label.permissionCount'),
        filterType: FilterType.NUMBER,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        filterable: false,
    },
    {
        field: 'userCount',
        headerName: strings('rolesAndPermissions:label.userCount'),
        filterType: FilterType.NUMBER,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        filterable: false,
    },
];
