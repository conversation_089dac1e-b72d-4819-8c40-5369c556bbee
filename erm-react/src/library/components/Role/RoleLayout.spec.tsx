import React from 'react';
import { render, screen } from 'test/utils/rtl';
import { waitFor } from '@testing-library/react';
import RoleLayout from './RoleLayout';
import { ViewRest } from 'api/generated/types';
import { strings } from 'common/utils/i18n';

const mockLoadEntries = jest.fn();

jest.mock('common/api/roles', () => ({
    useRrsiGetRolesUsingPostQuery: jest.fn((args) => mockLoadEntries(args)),
}));

const viewData = {
    views: [],
} as ViewRest;

jest.mock('view/rtkApi', () => ({
    useVrsGetViewsUsingGetQuery: jest.fn(() => ({
        data: viewData,
        isLoading: false,
        isSuccess: true,
        isError: false,
        refetch: jest.fn(),
    })),
    useVrsDeleteViewUsingDeleteMutation: jest.fn(() => [jest.fn(), { isLoading: false }]),
    useVrsCreateViewUsingPostMutation: jest.fn(() => [jest.fn(), { isLoading: false }]),
    useVrsUpdateViewUsingPutMutation: jest.fn(() => [jest.fn(), { isLoading: false }]),
}));

const roleName = 'Some role name';

describe('RoleLayout', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockLoadEntries.mockReturnValue({
            data: {
                records: [
                    {
                        id: 100111,
                        name: roleName,
                        description: 'Allows users to change their password',
                        permissionCount: 1,
                        userCount: 6,
                        userType: 'Data Entry',
                        dashboard: 'Some dashboard',
                    },
                ],
                totalCount: 1,
            },
            isFetching: false,
            isSuccess: true,
        });
    });

    it('renders basic layout elements', async () => {
        render(<RoleLayout />);

        expect(screen.getByPlaceholderText(strings('common:placeholder.searchFor'))).toBeInTheDocument();
        expect(screen.getByTestId('button-view-selector')).toBeInTheDocument();

        await waitFor(() => {
            expect(screen.getByText(roleName)).toBeInTheDocument();
        });
    });

    it('renders title when not in selector mode', async () => {
        render(<RoleLayout />);

        await waitFor(() => {
            expect(screen.getByTestId('Roles-heading')).toBeInTheDocument();
        });
    });

    it('does not render title when in selector mode', async () => {
        render(<RoleLayout isSelector={true} />);

        await waitFor(() => {
            expect(screen.getByText(roleName)).toBeInTheDocument();
        });
        expect(screen.queryByTestId('Roles-heading')).not.toBeInTheDocument();
    });

    it('calls loadEntries when component mounts', async () => {
        render(<RoleLayout />);

        await waitFor(() => {
            expect(mockLoadEntries).toHaveBeenCalled();
        });
    });

    it('passes additional expressions to API call', async () => {
        const additionalExpressions = [{ field: 'userType', operator: 'eq', value: 'Admin' }];

        render(<RoleLayout additionalExpressions={additionalExpressions} />);

        await waitFor(() => {
            expect(mockLoadEntries).toHaveBeenCalledWith(
                expect.objectContaining({
                    body: expect.arrayContaining(additionalExpressions),
                }),
            );
        });
    });

    it('handles multiselect mode', async () => {
        const mockOnSelect = jest.fn();
        const selected = [{ id: 1 }];

        render(
            <RoleLayout
                multiselect={true}
                selected={selected}
                onSelect={mockOnSelect}
            />,
        );

        await waitFor(() => {
            expect(screen.getByText(roleName)).toBeInTheDocument();
        });

        expect(screen.getByPlaceholderText(strings('common:placeholder.searchFor'))).toBeInTheDocument();
    });

    it('handles API error gracefully', async () => {
        mockLoadEntries.mockReturnValue({
            data: undefined,
            error: {
                status: 500,
                data: { message: 'API Error' },
            },
            isError: true,
            isSuccess: false,
            isLoading: false,
            isFetching: false,
        });

        render(<RoleLayout />);

        await waitFor(() => {
            expect(mockLoadEntries).toHaveBeenCalled();
        });
    });
});
