import React from 'react';
import Box from '@mui/material/Box';
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Typography from '@mui/material/Typography';

import { SortType } from '@protecht/ui-library/library/types';
import StyledDivider from '@protecht/ui-library/library/components/StyledDivider';

import { IdOnly } from 'app/types';
import { ViewContextType } from 'view/types';
import { ViewExpressionRest } from 'api/generated/types';
import { LibraryEntriesTable, SearchByFieldComponent, TableComponent, ViewSelectorComponent } from 'common/components/EntriesTable/LibraryEntriesTable';
import ToolbarContainer from 'common/components/ToolbarSpacing/ToolbarContainer';
import ToolbarGroup from 'common/components/ToolbarSpacing/ToolbarGroup';
import ApplicationLayout from 'common/layouts/ApplicationLayout';
import MainLayout from 'common/layouts/MainLayout';
import ContentLayout from 'common/layouts/ContentLayout';
import { strings } from 'common/utils/i18n';
import useRoleLoader from './useRoleLoader';
import { DEFAULT_ORDER_FIELD, DEFAULT_SEARCH_FIELD, RoleColDef } from './RoleDefinitions';

type Props = {
    additionalExpressions?: ViewExpressionRest[];
    isSelector?: boolean;
    multiselect?: boolean;
    selected?: IdOnly[];
    onSelect?: (currentSelection: IdOnly[]) => void;
    onLoadingChange?: (isLoading: boolean) => void;
};

type TitleProps = {
    title: string;
    context: ViewContextType;
};

const Titlebar: React.FC<TitleProps> = ({ title, context }) => (
    <ToolbarContainer
        disableGutters={false}
        variant="regular"
    >
        <ToolbarGroup>
            <Typography
                variant="h1"
                data-testid={`${context}-heading`}
            >
                {title}
            </Typography>
        </ToolbarGroup>
    </ToolbarContainer>
);

const RoleLayout: React.FC<Props> = ({ additionalExpressions, isSelector = false, multiselect = false, selected = [], onSelect, onLoadingChange }: Props) => {
    const theme = useTheme();
    const isSmallerScreen = useMediaQuery(theme.breakpoints.down('sm'));

    const { handleParamsChange, ...loaderResult } = useRoleLoader(additionalExpressions);

    return (
        <ApplicationLayout>
            {!isSelector && (
                <Titlebar
                    title={strings('library:title.rolesLibrary')}
                    context={ViewContextType.Roles}
                />
            )}
            <LibraryEntriesTable
                colDef={RoleColDef}
                compositeSearch={isSmallerScreen}
                onParamsChange={handleParamsChange}
                entries={loaderResult.data}
                isLoading={loaderResult.isFetching}
                onLoadingChange={onLoadingChange}
            >
                <MainLayout>
                    <ContentLayout
                        isSelector={isSelector}
                        toolbar={
                            <ToolbarContainer
                                sx={{
                                    height: '42px',
                                    display: 'flex',
                                    flexWrap: 'nowrap',
                                    width: '100%',
                                    paddingTop: '4px',
                                    paddingBottom: '10px',
                                }}
                            >
                                <ToolbarGroup sx={{ width: '100%' }}>
                                    <SearchByFieldComponent
                                        defaultSearchField={DEFAULT_SEARCH_FIELD}
                                        hideSelectField={isSmallerScreen}
                                        searchPlaceholder={strings('common:placeholder.searchFor')}
                                    />
                                    {!isSmallerScreen && (
                                        <StyledDivider
                                            orientation="vertical"
                                            sx={{ height: '20px' }}
                                            margin={0}
                                        />
                                    )}
                                    <Box
                                        sx={{
                                            flexShrink: 1,
                                            overflow: 'hidden',
                                            display: isSmallerScreen ? 'none' : 'unset',
                                        }}
                                    >
                                        <ViewSelectorComponent
                                            context={ViewContextType.Roles}
                                            defaultOrderField={DEFAULT_ORDER_FIELD}
                                            defaultOrderType={SortType.ASC}
                                            defaultSearchField={DEFAULT_SEARCH_FIELD}
                                            identityColumnName="name"
                                            isNarrowView={isSmallerScreen}
                                        />
                                    </Box>
                                </ToolbarGroup>
                            </ToolbarContainer>
                        }
                    >
                        <TableComponent
                            multiselect={multiselect}
                            selected={selected}
                            onSelect={onSelect}
                        />
                    </ContentLayout>
                </MainLayout>
            </LibraryEntriesTable>
        </ApplicationLayout>
    );
};

export default RoleLayout;
