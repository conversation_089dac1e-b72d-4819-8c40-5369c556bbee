import { baseInjectedApi } from 'api/generated/risk';
import { GetRiskEventsApiArg, GetRiskEventsApiResponse } from 'api/generated/types';

export const riskEventApiWithTags = baseInjectedApi.enhanceEndpoints({
    addTagTypes: ['riskEvents'],
    endpoints: {
        rersGetRiskEventUsingGet: {
            providesTags: (result, _error, arg) => (result ? [{ type: 'riskEvents', id: arg.riskEventId }] : []),
        },
    },
});

const riskEventApi = riskEventApiWithTags.injectEndpoints({
    endpoints: (builder) => ({
        getRiskEventsSearch: builder.query<GetRiskEventsApiResponse, GetRiskEventsApiArg>({
            query: (queryArg) => ({
                url: '/v1/api/risk/events/search',
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    tagType: queryArg.tagType,
                    tagOperator: queryArg.tagOperator,
                    tagIds: queryArg.tagIds,
                    viewId: queryArg.viewId,
                    statuses: queryArg.statuses,
                },
            }),
            providesTags: (result, _error, _arg) => (result ? [{ type: 'riskEvents', id: 'list' }] : []),
        }),
    }),
});

export const { useGetRiskEventsSearchQuery, useLazyGetRiskEventsSearchQuery, useRersGetRiskEventUsingGetQuery, usePrefetch } = riskEventApi;
