import React from 'react';
import { IdOnly } from 'app/types';
import { ViewContextType } from 'view/types';
import { DEFAULT_ORDER_FIELD, DEFAULT_SEARCH_FIELD, RiskEventColDef } from './RiskEventDefinitions';
import { TagContext } from 'common/types';
import { ViewExpressionRest } from 'api/generated/types';
import LibraryEntriesWithTagFilter from '../shared/LibraryEntriesWithTagFilter';
import { strings } from 'common/utils/i18n';
import useRiskEventLoader from './useRiskEventLoader';
import { RiskEvent } from './types';

type Props = {
    additionalExpressions?: ViewExpressionRest[];
    isSelector?: boolean;
    multiselect?: boolean;
    persistViewState?: boolean;
    selected?: IdOnly[];
    onSelect?: (currentSelection: IdOnly[]) => void;
    onLoadingChange?: (isLoading: boolean) => void;
};

const RiskEventLayout: React.FC<Props> = ({
    additionalExpressions,
    isSelector = false,
    multiselect = false,
    persistViewState = true,
    selected = [],
    onSelect,
    onLoadingChange,
}: Props) => {
    const { handleParamsChange, ...loaderResult } = useRiskEventLoader(additionalExpressions);

    return (
        <LibraryEntriesWithTagFilter<RiskEvent>
            colDef={RiskEventColDef}
            defaultOrderField={DEFAULT_ORDER_FIELD}
            defaultSearchField={DEFAULT_SEARCH_FIELD}
            isSelector={isSelector}
            loaderResult={loaderResult}
            onParamsChange={handleParamsChange}
            multiselect={multiselect}
            tagContext={TagContext.RISK_EVENT}
            title={strings('ermConstants:risk_events')}
            viewContext={ViewContextType.RiskEvents}
            selected={selected}
            persistViewState={persistViewState}
            onSelect={onSelect}
            onLoadingChange={onLoadingChange}
        />
    );
};

export default RiskEventLayout;
