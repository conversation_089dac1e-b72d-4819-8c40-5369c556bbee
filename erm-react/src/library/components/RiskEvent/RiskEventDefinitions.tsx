import React from 'react';
import { GridValueFormatterParams } from '@mui/x-data-grid-pro';

import { IdWithName } from 'app/types';
import { IdWithNameArrayFormatter } from 'library/utils';
import { strings } from 'common/utils/i18n';
import { FilterType } from 'view/types';
import { DATA_SYSTEM_COLUMN, DataGridColDef, SYSTEM_COLUMN } from 'common/types';
import { MIN_TABLE_COLUMN_WIDTH } from 'common/constants';
import { CellRenderDefault } from 'common/components/Table/Cell/CellWithTooltip';
import {
    createIdColumnDef,
    createNameColumnDef,
    createDescriptionColumnDef,
    createTagsColumnDef,
    createCreateDateColumnDef,
    createCreatedByColumnDef,
    createLastModifiedDateColumnDef,
    createLastModifiedByColumnDef,
} from '../shared/CommonColumnDefinitions';

export const DEFAULT_ORDER_FIELD = SYSTEM_COLUMN.ID;
export const DEFAULT_SEARCH_FIELD = 'name';

export const RiskEventColDef: DataGridColDef[] = [
    createIdColumnDef(),
    createNameColumnDef(),
    createDescriptionColumnDef(true, 'note'),
    {
        field: 'owner',
        headerName: strings('library:label.riskOwner'),
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        filterType: FilterType.STRING,
        valueFormatter: (params: GridValueFormatterParams) => (params.value ? (params.value as IdWithName).name : ''),
        renderCell: (params) => (
            <CellRenderDefault
                value={params.value}
                formattedValue={params.formattedValue}
                computedCellWidth={params.colDef.computedWidth}
            />
        ),
    },
    {
        field: 'riskCauses',
        apiField: 'causes',
        headerName: strings('library:label.riskCauses'),
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        filterType: FilterType.STRING,
        valueFormatter: IdWithNameArrayFormatter,
        renderCell: (params) => (
            <CellRenderDefault
                value={params.value}
                formattedValue={params.formattedValue}
                computedCellWidth={params.colDef.computedWidth}
            />
        ),
    },
    createTagsColumnDef(),
    { ...createCreateDateColumnDef(), apiField: DATA_SYSTEM_COLUMN.FIELD_CREATED_DATE },
    createCreatedByColumnDef(),
    createLastModifiedDateColumnDef(),
    createLastModifiedByColumnDef(),
];
