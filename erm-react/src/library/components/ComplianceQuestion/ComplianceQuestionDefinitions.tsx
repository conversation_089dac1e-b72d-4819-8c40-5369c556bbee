import { GridValueFormatterParams } from '@mui/x-data-grid-pro';
import { MIN_TABLE_COLUMN_WIDTH } from 'common/constants';
import { FilterType } from 'view/types';
import { strings } from 'common/utils/i18n';
import { getComplianceFrequencyLabel } from 'library/utils';
import { ComplianceFrequency } from 'library/types';
import { createIdColumnDef, createNameColumnDef, createDescriptionColumnDef, createTagsColumnDef } from '../shared/CommonColumnDefinitions';

export const DEFAULT_SEARCH_FIELD = 'name';
export const DEFAULT_ORDER_FIELD = 'name';

export const LibrarySelectorEntriesColDef = [
    createIdColumnDef(),
    createNameColumnDef(),
    createDescriptionColumnDef(),
    {
        field: 'category',
        headerName: strings('compliance:label.category'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },

    {
        field: 'riskEvents',
        headerName: strings('library:label.riskEvents'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params: GridValueFormatterParams) => {
            return params.value?.map((riskEvent) => riskEvent.name).join(', ');
        },
    },
    {
        field: 'controls',
        headerName: strings('library:label.controls'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params: GridValueFormatterParams) => {
            return params.value?.map((riskEvent) => riskEvent.name).join(', ');
        },
    },
    {
        field: 'defaultFrequency',
        headerName: strings('library:label.defaultFrequency'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params: GridValueFormatterParams) => getComplianceFrequencyLabel(params.value as ComplianceFrequency),
    },
    createTagsColumnDef(),
];
