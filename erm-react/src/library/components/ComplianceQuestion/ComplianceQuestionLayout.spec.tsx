import { render, screen } from 'test/utils/rtl';
import ComplianceQuestionLayout from './ComplianceQuestionLayout';
import React from 'react';
import { waitFor } from '@testing-library/react';
import { ViewRest } from 'api/generated/types';

const mockedDescription = '<b style="color: rgb(38,43,49);">test description</b>';

jest.mock('./rtkApi', () => {
    return {
        ...jest.requireActual('./rtkApi'),
        useGetComplianceQuestionsSearchQuery: jest.fn(() => ({
            data: {
                records: [{ id: 1, name: 'Test Compliance Question', description: mockedDescription }],
                totalCount: 1,
            },
            isFetching: false,
            isSuccess: true,
        })),
    };
});

const viewData = {
    views: [],
} as ViewRest;
jest.mock('view/rtkApi', () => ({
    ...jest.requireActual('view/rtkApi'),
    useVrsGetViewsUsingGetQuery: jest.fn(() => ({
        data: viewData,
        isLoading: false,
        isSuccess: true,
        isError: false,
        refetch: jest.fn(),
    })),
}));

describe('ComplianceQuestionLayout', () => {
    it('renders ComplianceQuestionLayout', async () => {
        render(<ComplianceQuestionLayout />);

        expect(screen.getByPlaceholderText(/search/i)).toBeInTheDocument();

        expect(screen.getByTestId('button-view-selector')).toBeInTheDocument();

        await waitFor(() => {
            expect(screen.getByText('Test Compliance Question')).toBeInTheDocument();
        });
    });

    it('renders description column with html content as styled text', async () => {
        render(<ComplianceQuestionLayout />);

        await waitFor(() => {
            expect(screen.getByText('test description')).toBeInTheDocument();
        });
        expect(screen.queryByText(mockedDescription)).not.toBeInTheDocument();
    });
});
