import React from 'react';

import { IdOnly } from 'app/types';
import { ViewContextType } from 'view/types';
import { LibrarySelectorEntriesColDef, DEFAULT_ORDER_FIELD, DEFAULT_SEARCH_FIELD } from './ComplianceQuestionDefinitions';
import { ViewExpressionRest } from 'api/generated/types';
import LibraryEntriesWithCategoryFilter from '../shared/LibraryEntriesWithCategoryFilter';
import { strings } from 'common/utils/i18n';
import useComplianceQuestionLoader from './useComplianceQuestionLoader';
import { ComplianceQuestion } from './types';

type Props = {
    additionalExpressions?: ViewExpressionRest[];
    isSelector?: boolean;
    multiselect?: boolean;
    persistViewState?: boolean;
    selected?: IdOnly[];
    onSelect?: (currentSelection: IdOnly[]) => void;
    onLoadingChange?: (isLoading: boolean) => void;
};

const ComplianceQuestionLayout: React.FC<Props> = ({
    additionalExpressions,
    isSelector = false,
    multiselect = false,
    persistViewState = true,
    selected = [],
    onSelect,
    onLoadingChange,
}: Props) => {
    const { handleParamsChange, ...loaderResult } = useComplianceQuestionLoader(additionalExpressions);

    return (
        <LibraryEntriesWithCategoryFilter<ComplianceQuestion>
            colDef={LibrarySelectorEntriesColDef}
            defaultOrderField={DEFAULT_ORDER_FIELD}
            defaultSearchField={DEFAULT_SEARCH_FIELD}
            loaderResult={loaderResult}
            isSelector={isSelector}
            onParamsChange={handleParamsChange}
            multiselect={multiselect}
            title={strings('ermConstants:compliance_questions')}
            viewContext={ViewContextType.ComplianceQuestions}
            selected={selected}
            persistViewState={persistViewState}
            onSelect={onSelect}
            onLoadingChange={onLoadingChange}
        />
    );
};

export default ComplianceQuestionLayout;
