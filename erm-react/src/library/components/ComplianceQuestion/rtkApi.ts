import { baseInjected<PERSON>pi as auditApi } from 'api/generated/compliances';
import { ComplianceQuestionsApiResponse, ComplianceQuestionsApiArg } from 'api/generated/types';

export const complianceQuestionsQApiWithTags = auditApi.enhanceEndpoints({
    addTagTypes: ['complianceQuestions'],
    endpoints: {
        cqrsGetControlUsingGet: {
            providesTags: (result, _error, _arg) => (result ? [{ type: 'complianceQuestions', id: result.id }] : []),
        },
    },
});

const api = complianceQuestionsQApiWithTags.injectEndpoints({
    endpoints: (builder) => ({
        getComplianceQuestionsSearch: builder.query<ComplianceQuestionsApiResponse, ComplianceQuestionsApiArg>({
            query: (queryArg) => ({
                url: '/v1/api/compliances/questions/search',
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    tagType: queryArg.tagType,
                    tagOperator: queryArg.tagOperator,
                    tagIds: queryArg.tagIds,
                    viewId: queryArg.viewId,
                    statuses: queryArg.statuses,
                },
            }),
            providesTags: (result, _error, _arg) => (result ? [{ type: 'complianceQuestions', id: 'list' }] : []),
        }),
    }),
});

export const { useCqrsGetControlUsingGetQuery, usePrefetch, useGetComplianceQuestionsSearchQuery } = api;
