import { ViewExpressionRest } from 'api/generated/types';
import { useGetComplianceQuestionsSearchQuery } from './rtkApi';
import useLibraryEntriesLoaderState from 'library/hooks/useLibraryEntriesLoaderState';
import { getLibraryLoaderState } from 'library/utils';

export const useComplianceQuestionLoader = (additionalExpressions?: ViewExpressionRest[]) => {
    const { queryArgs, enabled, handleParamsChange } = useLibraryEntriesLoaderState(additionalExpressions);

    const queryResult = useGetComplianceQuestionsSearchQuery(queryArgs!, {
        skip: !enabled || !queryArgs,
    });

    return getLibraryLoaderState(queryResult, handleParamsChange);
};

export default useComplianceQuestionLoader;
