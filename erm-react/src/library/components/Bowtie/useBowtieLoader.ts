import { ViewExpressionRest } from 'api/generated/types';
import { useBtciGetDiagramsUsingPostQuery } from 'bowtie/rtkApi';
import useLibraryEntriesLoaderState from 'library/hooks/useLibraryEntriesLoaderState';
import { getLibraryLoaderState } from 'library/utils';

export const useBowtieLoader = (additionalExpressions?: ViewExpressionRest[]) => {
    const { queryArgs, enabled, handleParamsChange } = useLibraryEntriesLoaderState(additionalExpressions);
    const { offset = 0, limit = 0, page, orderBy, orderType, groupBy, tagType, tagOperator, tagIds, viewId, filterContextRest } = queryArgs || {};

    const queryResult = useBtciGetDiagramsUsingPostQuery(
        {
            size: limit,
            page: page ?? (limit !== 0 ? offset / limit : 0),
            viewId,
            'sort-by': orderBy,
            'sort-dir': orderType ? (orderType === 'asc' ? 'ASC' : 'DESC') : undefined,
            'group-by': groupBy,
            'tag-type': tagType,
            tags: tagIds,
            'tag-operator': tagOperator,
            filterContextRest: filterContextRest!,
        },
        {
            skip: !enabled || !queryArgs,
        },
    );

    return getLibraryLoaderState(queryResult, handleParamsChange);
};

export default useBowtieLoader;
