import { DataGridColDef, FilterType } from '@protecht/ui-library/library/types';
import { MIN_TABLE_COLUMN_WIDTH } from 'common/constants';
import { strings } from 'common/utils/i18n';
import {
    createIdColumnDef,
    createNameColumnDef,
    createDescriptionColumnDef,
    createCreateDateColumnDef,
    createCreatedByColumnDef,
    createLastModifiedDateColumnDef,
    createLastModifiedByColumnDef,
    createStatusColumnDef,
    createTagsColumnDef,
} from '../shared/CommonColumnDefinitions';

export const DEFAULT_SEARCH_FIELD = 'name';
export const DEFAULT_ORDER_FIELD = 'name';

export const LibrarySelectorEntriesColDef: DataGridColDef[] = [
    createIdColumnDef(),
    createNameColumnDef(),
    createDescriptionColumnDef(false, undefined),
    {
        field: 'centralRiskEventName',
        headerName: strings('library:label.centralRiskEvent'),
        filterType: FilterType.STRING,
        sortable: false,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        groupable: false,
    },
    createCreatedByColumnDef(),
    createCreateDateColumnDef(),
    createLastModifiedByColumnDef(),
    createLastModifiedDateColumnDef(),
    createStatusColumnDef(),
    createTagsColumnDef(),
];
