import React, { useCallback, useMemo, useState } from 'react';
import { IdOnly } from 'app/types';
import ApplicationLayout from 'common/layouts/ApplicationLayout';
import MainLayout from 'common/layouts/MainLayout';
import ContentLayout from 'common/layouts/ContentLayout';
import { SortType } from 'ui/types';
import ToolbarContainer from 'common/components/ToolbarSpacing/ToolbarContainer';
import ToolbarGroup from 'common/components/ToolbarSpacing/ToolbarGroup';
import { ViewExpressionRest } from 'api/generated/types';
import { AddNewButtonComponent, LibraryEntriesTable, TableComponent, ViewSelectorComponent } from 'common/components/EntriesTable/LibraryEntriesTable';
import { GridColDef } from '@mui/x-data-grid';
import Selector from 'common/components/Selector';
import { SelectorType } from 'common/types';
import { getSelectorMetadata } from 'common/utils/definitions';
import SelectButtonComponent from 'common/components/EntriesTable/LibraryEntriesTable/SelectButtonComponent/SelectButtonComponent';
import StyledDivider from '@protecht/ui-library/library/components/StyledDivider';
import LabelWithTooltip from 'common/components/LabelWithTooltip';
import DeleteButtonComponent from 'common/components/EntriesTable/LibraryEntriesTable/DeleteButtonComponent/DeleteButtonComponent';
import useContainerQuery from '@protecht/ui-library/library/hooks/useContainerQuery';
import Box from '@mui/material/Box';
import { LibraryLoaderResult, LibraryTableParamsCallback } from 'library/types';

type Props<T extends IdOnly> = {
    additionalExpressions?: ViewExpressionRest[];
    colDef: GridColDef[];
    defaultOrderField: string;
    defaultSearchField: string;
    filterData?: any;
    identityColumnName: string;
    label?: string;
    labelTooltip?: string;
    loaderResult: LibraryLoaderResult<T>;
    multiselect?: boolean;
    selected?: T[];
    type: SelectorType;
    viewContext: string;
    onLoadingChange?: (isLoading: boolean) => void;
    onChange: (currentSelection: T[]) => void;
    onParamsChange: LibraryTableParamsCallback;
    readOnly?: boolean;
    hideAddButton?: boolean;
};

const LibraryEntriesTableFieldView = <T extends IdOnly>({
    additionalExpressions,
    colDef,
    defaultOrderField,
    defaultSearchField,
    filterData,
    identityColumnName,
    loaderResult,
    label,
    labelTooltip,
    multiselect = true,
    selected = [],
    viewContext,
    onChange,
    onLoadingChange,
    type,
    onParamsChange,
    readOnly = false,
    hideAddButton = false,
}: Props<T>) => {
    const [containerRef, isNarrowView] = useContainerQuery('max-width:450px');

    const selectorMetadata = useMemo(() => getSelectorMetadata(type), [type]);

    const [openSelector, setOpenSelector] = useState(false);

    const [tableSelection, setTableSelection] = useState<T[]>([]);

    const handleDeleteEntries = useCallback(() => {
        const newEntries = multiselect ? selected.filter((entry) => tableSelection.findIndex((selected) => selected.id === entry.id) === -1) : [];
        onChange(newEntries);
    }, [multiselect, onChange, selected, tableSelection]);

    return (
        <>
            <ApplicationLayout>
                <LibraryEntriesTable
                    colDef={colDef}
                    compositeSearch={false}
                    isLoading={loaderResult.isFetching || loaderResult.isLoading}
                    entries={loaderResult.data}
                    onLoadingChange={onLoadingChange}
                    additionalExpressions={additionalExpressions}
                    onParamsChange={onParamsChange}
                >
                    <MainLayout>
                        <ContentLayout
                            isSelector={true}
                            sx={{ margin: 0 }}
                            toolbar={
                                <ToolbarContainer
                                    sx={{
                                        height: '42px',
                                        display: 'flex',
                                        flexWrap: 'nowrap',
                                        width: '100%',
                                        paddingTop: '4px',
                                        paddingBottom: '10px',
                                    }}
                                >
                                    <ToolbarGroup sx={{ width: '100%' }}>
                                        <LabelWithTooltip
                                            label={label}
                                            tooltip={labelTooltip}
                                        />
                                        <ViewSelectorComponent
                                            context={viewContext}
                                            defaultOrderField={defaultOrderField}
                                            defaultOrderType={SortType.ASC}
                                            defaultSearchField={defaultSearchField}
                                            isNarrowView={isNarrowView}
                                            identityColumnName={identityColumnName}
                                        />
                                        {!readOnly && (
                                            <>
                                                <StyledDivider
                                                    orientation="vertical"
                                                    sx={{ height: '20px' }}
                                                    margin={0}
                                                />
                                                <DeleteButtonComponent
                                                    visible={selected.length > 0}
                                                    disabled={tableSelection.length === 0}
                                                    onClick={handleDeleteEntries}
                                                />
                                                <SelectButtonComponent onClick={() => setOpenSelector(true)} />
                                                {!hideAddButton && <AddNewButtonComponent />}
                                            </>
                                        )}
                                    </ToolbarGroup>
                                </ToolbarContainer>
                            }
                        >
                            <Box
                                ref={containerRef}
                                sx={{ height: '100%' }}
                            >
                                <TableComponent
                                    multiselect={multiselect}
                                    onSelect={setTableSelection}
                                    checkboxSelection={false}
                                />
                            </Box>
                        </ContentLayout>
                    </MainLayout>
                </LibraryEntriesTable>
            </ApplicationLayout>
            {openSelector && (
                <Selector
                    title={selectorMetadata.titleMultiple || selectorMetadata.title}
                    visible={openSelector}
                    multiselect={multiselect}
                    filterData={filterData}
                    selected={selected || []}
                    onSubmit={(currentSelection) => {
                        setOpenSelector(false);
                        onChange(currentSelection ?? []);
                    }}
                    type={type}
                    onClose={() => setOpenSelector(false)}
                />
            )}
        </>
    );
};

export default LibraryEntriesTableFieldView;
