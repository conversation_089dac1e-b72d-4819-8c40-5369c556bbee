import React, { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Typography from '@mui/material/Typography';
import useMediaQuery from '@mui/material/useMediaQuery';
import useTheme from '@mui/system/useTheme';
import { ObjectStatusFilter } from 'app/components/buttons/ObjectStatusFilter';
import { setRequestParams, setSearchProperty, setSearchValue, setSelectedView } from 'app/reducer';
import { getRequestParams, getSearchProperty, getSearchValue, getSelectedView } from 'app/selectors';
import { IdOnly } from 'app/types';
import ApplicationLayout from 'common/layouts/ApplicationLayout';
import MainLayout from 'common/layouts/MainLayout';
import ContentLayout from 'common/layouts/ContentLayout';
import { strings } from 'common/utils/i18n';
import { useDispatch, useSelector } from 'store';
import { SortType } from 'ui/types';
import { ViewContextType } from 'view/types';
import ToolbarContainer from 'common/components/ToolbarSpacing/ToolbarContainer';
import ToolbarGroup from 'common/components/ToolbarSpacing/ToolbarGroup';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import useTags from 'common/hooks/useTags';
import { TagContext } from 'common/types';
import { TagFilterParams, TagFilterTreeItem } from '@protecht/ui-library/library/types';
import StyledDivider from '@protecht/ui-library/library/components/StyledDivider';
import Grid from '@mui/material/Grid';
import { ViewRest } from 'api/generated/types';
import {
    AddNewButtonComponent,
    OpenTagFilterButtonComponent,
    SearchByFieldComponent,
    TableComponent,
    TagFilterComponent,
    ViewSelectorComponent,
} from 'common/components/EntriesTable/LibraryEntriesTable';
import Box from '@mui/material/Box';
import { LibraryEntriesTable } from 'common/components/EntriesTable/LibraryEntriesTable';
import { GridColDef } from '@mui/x-data-grid';
import { LibraryLoaderResult, LibraryTableParamsCallback } from 'library/types';

type Props<T extends IdOnly> = {
    colDef: GridColDef[];
    defaultOrderField: string;
    defaultSearchField: string;
    isSelector?: boolean;
    loaderResult: LibraryLoaderResult<T>;
    multiselect?: boolean;
    persistViewState: boolean;
    selected?: T[];
    tagContext: TagContext;
    title: string;
    viewContext: ViewContextType;
    onParamsChange: LibraryTableParamsCallback;
    onSelect?: (currentSelection: T[]) => void;
    onLoadingChange?: (isLoading: boolean) => void;
};

type TitleProps = {
    title: string;
    context: ViewContextType;
};

const Titlebar: FC<TitleProps> = ({ title, context }) => (
    <ToolbarContainer
        disableGutters={false}
        variant="regular"
    >
        <ToolbarGroup>
            <Typography
                variant="h1"
                data-testid={`${context}-heading`}
            >
                {title}
            </Typography>
        </ToolbarGroup>
    </ToolbarContainer>
);

const LibraryEntriesWithTagFilter = <T extends IdOnly>({
    colDef,
    defaultOrderField,
    defaultSearchField,
    isSelector = false,
    loaderResult,
    multiselect = false,
    selected = [],
    title,
    persistViewState,
    tagContext,
    viewContext,
    onParamsChange,
    onSelect,
    onLoadingChange,
}: Props<T>) => {
    const theme = useTheme();
    const isSmallerScreen = useMediaQuery(theme.breakpoints.down('sm'));

    const dispatch = useDispatch();

    const defaultSelectedView = useSelector(getSelectedView);
    const defaultSearchProperty = useSelector(getSearchProperty);
    const defaultSearchValue = useSelector(getSearchValue);
    const requestParams = useSelector(getRequestParams);
    const defaultTagFilterOperator = requestParams?.tagOperator;
    const defaultTagFilterIds = requestParams?.tagIds;
    const defaultTagFilterParams = useMemo(() => {
        return {
            tagOperator: defaultTagFilterOperator,
            tagIds: defaultTagFilterIds,
        };
    }, [defaultTagFilterIds, defaultTagFilterOperator]);

    const currentSelectedViewRef = useRef<ViewRest | null>();
    const currentSearchFieldRef = useRef<string>(defaultSearchProperty);
    const currentSearchValueRef = useRef<string>(defaultSearchValue);
    const currentTagFilterRef = useRef<TagFilterParams>(defaultTagFilterParams);

    useEffect(() => {
        // persist data on unmount
        return () => {
            if (persistViewState) {
                dispatch(setSelectedView(currentSelectedViewRef.current));
                dispatch(setSearchProperty(currentSearchFieldRef.current));
                dispatch(setSearchValue(currentSearchValueRef.current));
                dispatch(setRequestParams({ ...currentTagFilterRef.current }));
            }
        };
    }, [dispatch, persistViewState]);

    const { tagTree } = useTags({ tagContext });

    const [tagFilterVisible, setTagFilterVisible] = useState<boolean>(false);

    const toggleTagFilter = useCallback(() => {
        setTagFilterVisible((prev) => !prev);
    }, []);

    const onSearchFieldChanged = useCallback((searchField: string) => {
        currentSearchFieldRef.current = searchField;
    }, []);

    const onSearchValueChanged = useCallback((searchValue: string) => {
        currentSearchValueRef.current = searchValue;
    }, []);

    return (
        <ApplicationLayout>
            {!isSelector && (
                <Titlebar
                    title={title}
                    context={viewContext}
                />
            )}
            <LibraryEntriesTable
                colDef={colDef}
                compositeSearch={tagFilterVisible || isSmallerScreen}
                entries={loaderResult.data}
                isLoading={loaderResult.isLoading || loaderResult.isFetching}
                defaultTagFilterParams={defaultTagFilterParams}
                onParamsChange={onParamsChange}
                onLoadingChange={onLoadingChange}
            >
                <MainLayout
                    sidebarWidth={318}
                    sidebar={
                        tagFilterVisible && (
                            <Grid
                                container
                                direction="column"
                                flexWrap="nowrap"
                                overflow="hidden"
                                height="100%"
                            >
                                <Grid item>
                                    <ToolbarContainer sx={{ height: '42px', minHeight: '42px', alignItems: 'flex-start' }}>
                                        <ToolbarGroup
                                            flex={1}
                                            pt="4px"
                                            justifyContent="space-between"
                                        >
                                            <Typography variant="body2">{strings('common:label.filterByTag')}</Typography>
                                            <Button
                                                {...ButtonStyles.tableToolbarButton}
                                                aria-label="tag-filter-toggle"
                                                onClick={toggleTagFilter}
                                                variant="secondary"
                                            >
                                                {strings('common:button.hideFilter')}
                                            </Button>
                                        </ToolbarGroup>
                                    </ToolbarContainer>
                                </Grid>
                                <Grid
                                    item
                                    flex={1}
                                    overflow="hidden"
                                >
                                    <TagFilterComponent
                                        tagContext={tagContext}
                                        onTagFilterChanged={(tagFilter: TagFilterParams) => {
                                            currentTagFilterRef.current = tagFilter;
                                        }}
                                    />
                                </Grid>
                            </Grid>
                        )
                    }
                >
                    <ContentLayout
                        isSelector={isSelector}
                        sx={{
                            marginLeft: isSmallerScreen && tagFilterVisible ? 0 : '16px',
                        }}
                        toolbar={
                            <ToolbarContainer
                                sx={{
                                    height: '42px',
                                    display: 'flex',
                                    flexWrap: 'nowrap',
                                    width: '100%',
                                    paddingTop: '4px',
                                    paddingBottom: '10px',
                                }}
                            >
                                <ToolbarGroup sx={{ width: '100%' }}>
                                    {!tagFilterVisible && (
                                        <>
                                            <OpenTagFilterButtonComponent onClick={toggleTagFilter} />
                                            <StyledDivider
                                                orientation="vertical"
                                                sx={{ height: '20px' }}
                                                margin={0}
                                            />
                                        </>
                                    )}
                                    <SearchByFieldComponent
                                        defaultSearchField={defaultSearchProperty}
                                        defaultSearchValue={defaultSearchValue}
                                        hideSelectField={tagFilterVisible || isSmallerScreen}
                                        onSearchFieldChanged={onSearchFieldChanged}
                                        onSearchValueChanged={onSearchValueChanged}
                                    />
                                    {!isSelector && ( // todo
                                        <ObjectStatusFilter
                                            value={requestParams?.statuses}
                                            updateValue={(statuses) => dispatch(setRequestParams({ ...requestParams, statuses }))}
                                        />
                                    )}
                                    {!tagFilterVisible && !isSmallerScreen && (
                                        <StyledDivider
                                            orientation="vertical"
                                            sx={{ height: '20px' }}
                                            margin={0}
                                        />
                                    )}
                                    <Box
                                        sx={{
                                            flexShrink: 1,
                                            overflow: 'hidden',
                                            minWidth: '150px',
                                            display: tagFilterVisible || isSmallerScreen ? 'none' : 'unset',
                                        }}
                                    >
                                        <ViewSelectorComponent
                                            context={viewContext}
                                            defaultOrderField={defaultOrderField}
                                            defaultOrderType={SortType.ASC}
                                            defaultSearchField={defaultSearchField}
                                            defaultSelectedView={defaultSelectedView}
                                            identityColumnName="name"
                                            isNarrowView={isSmallerScreen}
                                            onViewSelected={(view) => {
                                                currentSelectedViewRef.current = view;
                                            }}
                                        />
                                    </Box>
                                    <StyledDivider
                                        orientation="vertical"
                                        sx={{ height: '20px' }}
                                        margin={0}
                                    />
                                    <AddNewButtonComponent filterVisible={tagFilterVisible} />
                                </ToolbarGroup>
                            </ToolbarContainer>
                        }
                    >
                        <TableComponent
                            multiselect={multiselect}
                            selected={selected}
                            onSelect={onSelect}
                            tagTree={tagTree as TagFilterTreeItem[]}
                        />
                    </ContentLayout>
                </MainLayout>
            </LibraryEntriesTable>
        </ApplicationLayout>
    );
};

export default LibraryEntriesWithTagFilter;
