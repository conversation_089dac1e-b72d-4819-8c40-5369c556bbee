import React, { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Typography from '@mui/material/Typography';
import useMediaQuery from '@mui/material/useMediaQuery';
import useTheme from '@mui/system/useTheme';
import { ObjectStatusFilter } from 'app/components/buttons/ObjectStatusFilter';
import { setRequestParams, setSearchProperty, setSearchValue, setSelectedView } from 'app/reducer';
import { getRequestParams, getSearchProperty, getSearchValue, getSelectedView } from 'app/selectors';
import { IdOnly } from 'app/types';
import ApplicationLayout from 'common/layouts/ApplicationLayout';
import MainLayout from 'common/layouts/MainLayout';
import ContentLayout from 'common/layouts/ContentLayout';
import { strings } from 'common/utils/i18n';
import { useDispatch, useSelector } from 'store';
import { SortType } from 'ui/types';
import { ViewContextType } from 'view/types';
import ToolbarContainer from 'common/components/ToolbarSpacing/ToolbarContainer';
import ToolbarGroup from 'common/components/ToolbarSpacing/ToolbarGroup';
import Button from '@protecht/ui-library/library/components/Button';
import StyledDivider from '@protecht/ui-library/library/components/StyledDivider';
import Grid from '@mui/material/Grid';
import { ViewRest } from 'api/generated/types';
import {
    AddNewButtonComponent,
    OpenCategoryFilterButtonComponent,
    SearchByFieldComponent,
    TableComponent,
    ViewSelectorComponent,
} from 'common/components/EntriesTable/LibraryEntriesTable';
import Box from '@mui/material/Box';
import { LibraryEntriesTable } from 'common/components/EntriesTable/LibraryEntriesTable';
import { GridColDef } from '@mui/x-data-grid';
import { LibraryLoaderResult, LibraryTableParamsCallback } from 'library/types';

type Props<T extends IdOnly> = {
    colDef: GridColDef[];
    defaultOrderField: string;
    defaultSearchField: string;
    isSelector?: boolean;
    loaderResult: LibraryLoaderResult<T>;
    multiselect?: boolean;
    persistViewState: boolean;
    selected?: T[];
    title: string;
    viewContext: ViewContextType;
    onSelect?: (currentSelection: T[]) => void;
    onLoadingChange?: (isLoading: boolean) => void;
    onParamsChange: LibraryTableParamsCallback;
};

type TitleProps = {
    title: string;
    context: ViewContextType;
};

const Titlebar: FC<TitleProps> = ({ title, context }) => (
    <ToolbarContainer
        disableGutters={false}
        variant="regular"
    >
        <ToolbarGroup>
            <Typography
                variant="h1"
                data-testid={`${context}-heading`}
            >
                {title}
            </Typography>
        </ToolbarGroup>
    </ToolbarContainer>
);

const LibraryEntriesWithCategoryFilter = <T extends IdOnly>({
    colDef,
    defaultOrderField,
    defaultSearchField,
    isSelector = false,
    loaderResult,
    multiselect = false,
    selected = [],
    title,
    persistViewState,
    viewContext,
    onSelect,
    onLoadingChange,
    onParamsChange,
}: Props<T>) => {
    const theme = useTheme();
    const isSmallerScreen = useMediaQuery(theme.breakpoints.down('sm'));

    const dispatch = useDispatch();

    const defaultSelectedView = useSelector(getSelectedView);
    const defaultSearchProperty = useSelector(getSearchProperty);
    const defaultSearchValue = useSelector(getSearchValue);
    const requestParams = useSelector(getRequestParams);
    const defaultCategoryFilterOperator = requestParams?.tagOperator; // todo
    const defaultCategoryFilterIds = requestParams?.tagIds; // todo
    const defaultCategoryFilterParams = useMemo(() => {
        return {
            tagOperator: defaultCategoryFilterOperator, // todo
            tagIds: defaultCategoryFilterIds, // todo
        };
    }, [defaultCategoryFilterIds, defaultCategoryFilterOperator]);

    const currentSelectedViewRef = useRef<ViewRest | null>();
    const currentSearchFieldRef = useRef<string>(defaultSearchProperty);
    const currentSearchValueRef = useRef<string>(defaultSearchValue);
    const currentCategoryFilterRef = useRef<any>(defaultCategoryFilterParams); // todo

    useEffect(() => {
        // persist data on unmount
        return () => {
            if (persistViewState) {
                dispatch(setSelectedView(currentSelectedViewRef.current));
                dispatch(setSearchProperty(currentSearchFieldRef.current));
                dispatch(setSearchValue(currentSearchValueRef.current));
                dispatch(setRequestParams({ ...currentCategoryFilterRef.current }));
            }
        };
    }, [dispatch, persistViewState]);

    const [categoryFilterVisible, setCategoryFilterVisible] = useState<boolean>(false);

    const toggleCategoryFilter = useCallback(() => {
        setCategoryFilterVisible((prev) => !prev);
    }, []);

    const onSearchFieldChanged = useCallback((searchField: string) => {
        currentSearchFieldRef.current = searchField;
    }, []);

    const onSearchValueChanged = useCallback((searchValue: string) => {
        currentSearchValueRef.current = searchValue;
    }, []);

    return (
        <ApplicationLayout>
            {!isSelector && (
                <Titlebar
                    title={title}
                    context={viewContext}
                />
            )}
            <LibraryEntriesTable
                colDef={colDef}
                compositeSearch={categoryFilterVisible || isSmallerScreen}
                isLoading={loaderResult.isFetching || loaderResult.isLoading}
                entries={loaderResult.data}
                // defaultTagFilterParams={defaultCategoryFilterParams}
                onLoadingChange={onLoadingChange}
                onParamsChange={onParamsChange}
            >
                <MainLayout
                    sidebarWidth={318}
                    sidebar={
                        categoryFilterVisible && (
                            <Grid
                                container
                                direction="column"
                                flexWrap="nowrap"
                                overflow="hidden"
                                height="100%"
                            >
                                <Grid item>
                                    <ToolbarContainer sx={{ height: '42px', minHeight: '42px', alignItems: 'flex-start' }}>
                                        <ToolbarGroup
                                            flex={1}
                                            pt="4px"
                                            justifyContent="space-between"
                                        >
                                            <Typography variant="body2">{strings('common:label.filterByCategory')}</Typography>
                                            <Button
                                                size="medium"
                                                aria-label="category-filter-toggle"
                                                onClick={toggleCategoryFilter}
                                                variant="secondary"
                                            >
                                                {strings('common:button.hideFilter')}
                                            </Button>
                                        </ToolbarGroup>
                                    </ToolbarContainer>
                                </Grid>
                                <Grid
                                    item
                                    flex={1}
                                    overflow="hidden"
                                >
                                    {/* // todo add category filter
                                        <TagFilterComponent
                                        tagContext={tagContext}
                                        onTagFilterChanged={(tagFilter: TagFilterParams) => {
                                            currentTagFilterRef.current = tagFilter;
                                        }}
                                    /> */}
                                </Grid>
                            </Grid>
                        )
                    }
                >
                    <ContentLayout
                        isSelector={isSelector}
                        sx={{
                            marginLeft: isSmallerScreen && categoryFilterVisible ? 0 : '16px',
                        }}
                        toolbar={
                            <ToolbarContainer
                                sx={{
                                    height: '42px',
                                    display: 'flex',
                                    flexWrap: 'nowrap',
                                    width: '100%',
                                    paddingTop: '4px',
                                    paddingBottom: '10px',
                                }}
                            >
                                <ToolbarGroup sx={{ width: '100%' }}>
                                    {!categoryFilterVisible && (
                                        <>
                                            <OpenCategoryFilterButtonComponent onClick={toggleCategoryFilter} />
                                            <StyledDivider
                                                orientation="vertical"
                                                sx={{ height: '20px' }}
                                                margin={0}
                                            />
                                        </>
                                    )}
                                    <SearchByFieldComponent
                                        defaultSearchField={defaultSearchProperty}
                                        defaultSearchValue={defaultSearchValue}
                                        hideSelectField={categoryFilterVisible || isSmallerScreen}
                                        onSearchFieldChanged={onSearchFieldChanged}
                                        onSearchValueChanged={onSearchValueChanged}
                                    />
                                    {!isSelector && ( // todo
                                        <ObjectStatusFilter
                                            value={requestParams?.statuses}
                                            updateValue={(statuses) => dispatch(setRequestParams({ ...requestParams, statuses }))}
                                        />
                                    )}
                                    {!categoryFilterVisible && !isSmallerScreen && (
                                        <StyledDivider
                                            orientation="vertical"
                                            sx={{ height: '20px' }}
                                            margin={0}
                                        />
                                    )}
                                    <Box
                                        sx={{
                                            flexShrink: 1,
                                            overflow: 'hidden',
                                            minWidth: '150px',
                                            display: categoryFilterVisible || isSmallerScreen ? 'none' : 'unset',
                                        }}
                                    >
                                        <ViewSelectorComponent
                                            context={viewContext}
                                            defaultOrderField={defaultOrderField}
                                            defaultOrderType={SortType.ASC}
                                            defaultSearchField={defaultSearchField}
                                            defaultSelectedView={defaultSelectedView}
                                            identityColumnName="name"
                                            isNarrowView={isSmallerScreen}
                                            onViewSelected={(view) => {
                                                currentSelectedViewRef.current = view;
                                            }}
                                        />
                                    </Box>
                                    <StyledDivider
                                        orientation="vertical"
                                        sx={{ height: '20px' }}
                                        margin={0}
                                    />
                                    <AddNewButtonComponent filterVisible={categoryFilterVisible} />
                                </ToolbarGroup>
                            </ToolbarContainer>
                        }
                    >
                        <TableComponent
                            multiselect={multiselect}
                            selected={selected}
                            onSelect={onSelect}
                        />
                    </ContentLayout>
                </MainLayout>
            </LibraryEntriesTable>
        </ApplicationLayout>
    );
};

export default LibraryEntriesWithCategoryFilter;
