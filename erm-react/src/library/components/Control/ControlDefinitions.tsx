import React from 'react';
import { GridValueFormatterParams } from '@mui/x-data-grid-pro';

import { ComplianceFrequency } from 'library/types';
import { getComplianceFrequencyLabel, IdWithNameArrayFormatter } from 'library/utils';
import { strings } from 'common/utils/i18n';
import { DATA_SYSTEM_COLUMN, DataGridColDef, SYSTEM_COLUMN } from 'common/types';
import { MIN_TABLE_COLUMN_WIDTH } from 'common/constants';
import { FilterType } from 'view/types';
import { CellRenderDefault } from 'common/components/Table/Cell/CellWithTooltip';
import {
    createIdColumnDef,
    createNameColumnDef,
    createDescriptionColumnDef,
    createTagsColumnDef,
    createCreateDateColumnDef,
    createCreatedByColumnDef,
    createLastModifiedDateColumnDef,
    createLastModifiedByColumnDef,
} from '../shared/CommonColumnDefinitions';

export const DEFAULT_ORDER_FIELD = SYSTEM_COLUMN.ID;
export const DEFAULT_SEARCH_FIELD = 'name';

export const ControlColDef: DataGridColDef[] = [
    createIdColumnDef(),
    createNameColumnDef(),
    createDescriptionColumnDef(),
    {
        field: 'riskEvents',
        headerName: strings('library:label.riskEvents'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: IdWithNameArrayFormatter,
        renderCell: (params) => (
            <CellRenderDefault
                value={params.value}
                formattedValue={params.formattedValue}
                computedCellWidth={params.colDef.computedWidth}
            />
        ),
    },
    {
        field: 'defaultFrequency',
        headerName: strings('library:label.defaultFrequency'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params: GridValueFormatterParams) => getComplianceFrequencyLabel(params.value as ComplianceFrequency),
        renderCell: (params) => (
            <CellRenderDefault
                value={params.value}
                formattedValue={params.formattedValue}
                computedCellWidth={params.colDef.computedWidth}
            />
        ),
    },
    createTagsColumnDef(),
    { ...createCreateDateColumnDef(), apiField: DATA_SYSTEM_COLUMN.FIELD_CREATED_DATE },
    createCreatedByColumnDef(),
    createLastModifiedDateColumnDef(),
    createLastModifiedByColumnDef(),
];
