import React from 'react';

import { IdOnly } from 'app/types';
import { strings } from 'common/utils/i18n';
import { ViewContextType } from 'view/types';
import { ControlColDef, DEFAULT_ORDER_FIELD, DEFAULT_SEARCH_FIELD } from './ControlDefinitions';
import { TagContext } from 'common/types';
import LibraryEntriesWithTagFilter from '../shared/LibraryEntriesWithTagFilter';
import { ViewExpressionRest } from 'api/generated/types';
import { useControlsLoader } from './useControlsLoader';
import { RiskControl } from './types';

type Props = {
    additionalExpressions?: ViewExpressionRest[];
    isSelector?: boolean;
    multiselect?: boolean;
    persistViewState?: boolean;
    selected?: IdOnly[];
    onSelect?: (currentSelection: IdOnly[]) => void;
    onLoadingChange?: (isLoading: boolean) => void;
};

const ControlLayout: React.FC<Props> = ({
    additionalExpressions,
    isSelector = false,
    multiselect = false,
    persistViewState = true,
    selected = [],
    onSelect,
    onLoadingChange,
}: Props) => {
    const { handleParamsChange, ...loaderResult } = useControlsLoader(additionalExpressions);

    return (
        <LibraryEntriesWithTagFilter<RiskControl>
            colDef={ControlColDef}
            defaultOrderField={DEFAULT_ORDER_FIELD}
            defaultSearchField={DEFAULT_SEARCH_FIELD}
            onParamsChange={handleParamsChange}
            isSelector={isSelector}
            loaderResult={loaderResult}
            multiselect={multiselect}
            tagContext={TagContext.CONTROL}
            title={strings('ermConstants:controls')}
            viewContext={ViewContextType.Controls}
            selected={selected}
            persistViewState={persistViewState}
            onSelect={onSelect}
            onLoadingChange={onLoadingChange}
        />
    );
};

export default ControlLayout;
