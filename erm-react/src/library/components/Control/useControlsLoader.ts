import { ViewExpressionRest } from 'api/generated/types';
import { useGetRiskControlsSearchQuery } from './rtkApi';
import useLibraryEntriesLoaderState from 'library/hooks/useLibraryEntriesLoaderState';
import { getLibraryLoaderState } from 'library/utils';

export const useControlsLoader = (additionalExpressions?: ViewExpressionRest[]) => {
    const { queryArgs, enabled, handleParamsChange } = useLibraryEntriesLoaderState(additionalExpressions);

    const queryResult = useGetRiskControlsSearchQuery(queryArgs!, {
        skip: !queryArgs || !enabled,
    });

    return getLibraryLoaderState(queryResult, handleParamsChange);
};

export default useControlsLoader;
