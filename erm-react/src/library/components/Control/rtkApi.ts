import { baseInjected<PERSON>pi } from 'api/generated/risk';
import { GetRiskControlApiArg, GetRiskControlApiResponse } from 'api/generated/types';

const riskControlApiWithTags = baseInjectedApi.enhanceEndpoints({
    addTagTypes: ['riskControls'],
    endpoints: {
        crsGetControlUsingGet: {
            providesTags: (result, _error, arg) => (result ? [{ type: 'riskControls', id: arg.riskControlId }] : []),
        },
    },
});

const riskControlApi = riskControlApiWithTags.injectEndpoints({
    endpoints: (builder) => ({
        getRiskControlsSearch: builder.query<GetRiskControlApiResponse, GetRiskControlApiArg>({
            query: (queryArg) => ({
                url: '/v1/api/risk/controls/search',
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    tagType: queryArg.tagType,
                    tagOperator: queryArg.tagOperator,
                    tagIds: queryArg.tagIds,
                    viewId: queryArg.viewId,
                    statuses: queryArg.statuses,
                },
            }),
            providesTags: (result, _error, _arg) => (result ? [{ type: 'riskControls', id: 'list' }] : []),
        }),
    }),
});

export const { useGetRiskControlsSearchQuery, useLazyGetRiskControlsSearchQuery, useCrsGetControlUsingGetQuery, usePrefetch } = riskControlApi;
