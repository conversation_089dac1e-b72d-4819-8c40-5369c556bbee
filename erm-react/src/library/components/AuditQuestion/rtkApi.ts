import { baseInjected<PERSON>pi as auditApi } from 'api/generated/audit';
import { AqrsGetAuditQuestionsUsingPostApiArg, AqrsGetAuditQuestionsUsingPostApiResponse } from 'api/generated/types';

export const auditApiWithTags = auditApi.enhanceEndpoints({
    addTagTypes: ['auditQuestions'],
    endpoints: {
        aqrsGetQuestionUsingGet: {
            providesTags: (result, _error, arg) => (result ? [{ type: 'auditQuestions', id: arg.questionId }] : []),
        },
    },
});

const api = auditApiWithTags.injectEndpoints({
    endpoints: (builder) => ({
        getAuditQuestionsSearch: builder.query<AqrsGetAuditQuestionsUsingPostApiResponse, AqrsGetAuditQuestionsUsingPostApiArg>({
            query: (queryArg) => ({
                url: '/v1/api/audit/auditquestions',
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                },
            }),
            providesTags: (result, _error, _arg) => (result ? [{ type: 'auditQuestions', id: 'list' }] : []),
        }),
    }),
});

export const { useGetAuditQuestionsSearchQuery, useAqrsGetQuestionUsingGetQuery, usePrefetch } = api;
