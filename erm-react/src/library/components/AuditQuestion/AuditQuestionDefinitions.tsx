import { GridValueFormatterParams } from '@mui/x-data-grid-pro';
import { MIN_TABLE_COLUMN_WIDTH } from 'common/constants';
import { FilterType } from 'view/types';
import { strings } from 'common/utils/i18n';
import { createIdColumnDef, createTagsColumnDef } from '../shared/CommonColumnDefinitions';

export const DEFAULT_SEARCH_FIELD = 'question';
export const DEFAULT_ORDER_FIELD = 'question';

export const LibrarySelectorEntriesColDef = [
    createIdColumnDef(),
    {
        field: 'question',
        headerName: strings('library:label.question'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    createTagsColumnDef(),
    {
        field: 'programs',
        apiField: 'programsAsString',
        headerName: strings('library:label.programs'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params: GridValueFormatterParams) => {
            return params.value?.map((program) => program.name).join(', ');
        },
    },
    {
        field: 'approval',
        apiField: 'questionstatus',
        headerName: strings('library:label.approval'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
];
