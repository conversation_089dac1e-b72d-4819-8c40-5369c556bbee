import { ViewExpressionRest } from 'api/generated/types';
import { useGetAuditQuestionsSearchQuery } from './rtkApi';
import useLibraryEntriesLoaderState from 'library/hooks/useLibraryEntriesLoaderState';
import { getLibraryLoaderState } from 'library/utils';

export const useAuditQuestionLoader = (additionalExpressions?: ViewExpressionRest[]) => {
    const { queryArgs, enabled, handleParamsChange } = useLibraryEntriesLoaderState(additionalExpressions);

    const queryResult = useGetAuditQuestionsSearchQuery(queryArgs!, {
        skip: !enabled || !queryArgs,
    });

    return getLibraryLoaderState(queryResult, handleParamsChange);
};

export default useAuditQuestionLoader;
