import { IdOnly, SearchRequestParams } from '@protecht/ui-library/library/types';
import { TagTypeRest, TagRest as RtkTagRest, ViewExpressionRest } from 'api/generated/types';
import { IdWithName, IdWithNameAndStatusRest } from 'app/types';

export interface State {
    errors: string[];
    previousPath: string | null;
    currentPath: string | null;
}

export interface StoreState {
    library: State;
}

export enum TableRestPath {
    BU = 'businessunits',
    // Country = 'countries',
    // State = 'states',
    Role = 'roles',
    User = 'users',
    Tag = 'tags/search',
    TagCategory = 'tags/category/search',
    TagSuggest = 'tags/suggest',
    // Question = 'questions',
    RiskCause = 'risk/causes/search',
    RiskEvent = 'risk/events/search',
    AuditQuestion = 'audit/questions',
    Control = 'risk/controls/search',
    // Consequence = 'consequences',
    // Likelihood = 'likelihoods',
    // KRI = 'kris',
    RegisterEntry = '/entries/{regId}',
}

export type ControlRest = IdWithNameAndStatusRest & {
    description: string;
    defaultFrequency: ComplianceFrequency;
    tags: IdWithName[];
    riskEvents: IdWithNameAndStatusRest[];
    createDate: Date;
    createdBy: IdWithName;
    lastModifiedDate: Date;
    lastModifiedBy: IdWithName;
};

export type RiskCauseRest = IdWithNameAndStatusRest & {
    description: string;
    tags: IdWithName[];
    completed: boolean;
    createDate: Date;
    createdBy: IdWithName;
    lastModifiedDate: Date;
    lastModifiedBy: IdWithName;
};

export type RiskEventRest = IdWithNameAndStatusRest & {
    description: string;
    tags: IdWithName[];
    owner: IdWithName;
    completed: boolean;
    // currently not supported in REST response data
    // riskCauses: IdWithNameAndStatusRest[];
    createDate: Date;
    createdBy: string;
    lastModifiedDate: Date;
    lastModifiedBy: string;
};

export type UserRest = IdWithNameAndStatusRest & {
    loginId: string;
    userName: string;
    email: string;
    instantMessenger: string;
    contactNumber: string;
    mobile: string;
    position: string;
    employeeId: string;
    dateFormatPattern: string;
    timeFormatPattern: string;
    homeDashboardUrl: string;
    businessUnit: IdWithName;
    businessUnits: IdWithName[];
    manager: IdWithName;
    roles: IdWithName[];
    administrative: boolean;
    lastLoginDate: Date;
    secret: string;
    disableAccess: boolean;
};

export type TagsRest = TagRest[];
export type TagRest = RtkTagRest;

export type TagType = TagTypeRest & { id: number; name: string };

export type TagTable = IdWithName & {
    value: string;
    preview: string;
    type: TagTypeRest;
};

export enum ComplianceFrequency {
    ON_DEMAND = 'O',
    DAILY = 'D',
    WEEKLY = 'W',
    FORTNIGHTLY = 'F',
    MONTHLY = 'M',
    BIMONTHLY = 'C',
    QUARTERLY = 'Q',
    SEMI_ANNUALLY = 'S',
    ANNUALLY = 'A',
    BIENNIALLY = 'B',
    TRIENNIALLY = 'T',
    QUINQUENNIALLY = 'U',
    NEVER = 'N',
    NOTSET = '-',
}

export enum ComplianceFrequencyLabel {
    ON_DEMAND_LABEL = 'On Demand',
    DAILY_LABEL = 'Daily',
    WEEKLY_LABEL = 'Weekly',
    FORTNIGHTLY_LABEL = 'Fortnightly',
    MONTHLY_LABEL = 'Monthly',
    BIMONTHLY_LABEL = 'Bimonthly',
    QUARTERLY_LABEL = 'Quarterly',
    SEMI_ANNUALLY_LABEL = 'Semi-Annually',
    ANNUALLY_LABEL = 'Annually',
    BIENNIALLY_LABEL = 'Biennially',
    TRIENNIALLY_LABEL = 'Triennially (3 Years)',
    QUINQUENNIALLY_LABEL = 'Quinquennially (5 Years)',
    NEVER_LABEL = 'Never',
    NOTSET_LABEL = 'Not Set',
}

export type LibraryEntriesResponse<T extends { id?: number }> = {
    maxPage?: number;
    records?: T[];
    response?: T;
    totalCount?: number;
};

export type LibraryEntries<T extends IdOnly> = {
    maxPage?: number;
    records?: T[];
    response?: T;
    totalCount?: number;
};

export type LibraryLoaderResult<T extends IdOnly> = {
    data: LibraryEntries<T> | undefined;
    isLoading: boolean;
    isFetching: boolean;
    error: any;
    refetch: () => void;
};

export type LibraryTableParamsCallback = (params: {
    searchRequestParams: SearchRequestParams;
    apiExpressions: ViewExpressionRest[] | undefined;
    enabled: boolean;
}) => void;
