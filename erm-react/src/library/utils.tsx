import { styled } from '@mui/material/styles';
import { GridValueFormatterParams } from '@mui/x-data-grid-pro';
import { SearchRequestParams } from '@protecht/ui-library/library/types';
import { TypedUseQueryHookResult } from '@reduxjs/toolkit/dist/query/react';
import { ViewExpressionRest } from 'api/generated/types';
import { IdWithName } from 'app/types';
import { ComplianceFrequency, ComplianceFrequencyLabel, LibraryEntries, LibraryEntriesResponse, TagRest, TagsRest, TagTable } from 'library/types';
import React from 'react';

export const restTagsToTable = (tags: TagsRest): TagTable[] => {
    if (!tags) {
        return [];
    }
    return tags.map<TagTable>((tag: TagRest) => {
        return {
            //id is always present in REST response, it is not provided only when we create new tag
            id: tag.id!,
            value: tag.value!,
            preview: `${tag.type?.name}:${tag.value}`,
            type: tag.type!,
            name: '',
        };
    });
};

export const IdWithNameArrayFormatter = (params: GridValueFormatterParams) => {
    // TODO: investigate why is there a string that needs to be split at first
    return (params.value && Array.isArray(params.value) ? (params.value as Array<IdWithName>) : [])
        .map((item) => item.name)
        .sort((name1, name2) => name1.localeCompare(name2))
        .join(', ');
};

// Ensure that the field this renderer is used for was processed by antisamy validator
const StyledFieldCell = styled('div')({
    '& > *': {
        display: 'inline',
    },
    display: 'inline',
});
export const HtmlFieldCellRenderer = ({ htmlValue }: { htmlValue: string }) => {
    return <StyledFieldCell dangerouslySetInnerHTML={{ __html: htmlValue ? htmlValue : '' }} />;
};

export const getComplianceFrequencyLabel = (complianceFrequency?: ComplianceFrequency): ComplianceFrequencyLabel | null => {
    switch (complianceFrequency) {
        case ComplianceFrequency.ANNUALLY: {
            return ComplianceFrequencyLabel.ANNUALLY_LABEL;
        }
        case ComplianceFrequency.BIENNIALLY: {
            return ComplianceFrequencyLabel.BIENNIALLY_LABEL;
        }
        case ComplianceFrequency.BIMONTHLY: {
            return ComplianceFrequencyLabel.BIMONTHLY_LABEL;
        }
        case ComplianceFrequency.DAILY: {
            return ComplianceFrequencyLabel.DAILY_LABEL;
        }
        case ComplianceFrequency.FORTNIGHTLY: {
            return ComplianceFrequencyLabel.FORTNIGHTLY_LABEL;
        }
        case ComplianceFrequency.MONTHLY: {
            return ComplianceFrequencyLabel.MONTHLY_LABEL;
        }
        case ComplianceFrequency.NEVER: {
            return ComplianceFrequencyLabel.NEVER_LABEL;
        }
        case ComplianceFrequency.QUARTERLY: {
            return ComplianceFrequencyLabel.QUARTERLY_LABEL;
        }
        case ComplianceFrequency.SEMI_ANNUALLY: {
            return ComplianceFrequencyLabel.SEMI_ANNUALLY_LABEL;
        }
        case ComplianceFrequency.TRIENNIALLY: {
            return ComplianceFrequencyLabel.TRIENNIALLY_LABEL;
        }
        case ComplianceFrequency.WEEKLY: {
            return ComplianceFrequencyLabel.WEEKLY_LABEL;
        }
        case ComplianceFrequency.ON_DEMAND: {
            return ComplianceFrequencyLabel.ON_DEMAND_LABEL;
        }
        case ComplianceFrequency.NOTSET: {
            return ComplianceFrequencyLabel.NOTSET_LABEL;
        }
        default: {
            return null;
        }
    }
};

// todo can be removed once types include required id
const transformData = <T extends { id?: number }>(queryResult: LibraryEntriesResponse<T> | undefined): LibraryEntries<T & { id: number }> | undefined => {
    if (!queryResult) {
        return undefined;
    }

    return {
        ...queryResult,
        records: queryResult.records?.map((record) => ({
            ...record,
            id: record.id || 0,
        })),
        response: queryResult.response ? { ...queryResult.response, id: queryResult.response.id || 0 } : undefined,
    };
};

export const getLibraryLoaderState = <T extends { id?: number }>(
    queryResult: TypedUseQueryHookResult<LibraryEntriesResponse<T>, any, any>,
    handleParamsChange: (params: { searchRequestParams: SearchRequestParams; apiExpressions: ViewExpressionRest[] | undefined; enabled: boolean }) => void,
) => ({
    data: transformData(queryResult.data),
    isLoading: queryResult.isLoading,
    isFetching: queryResult.isFetching,
    error: queryResult.error,
    refetch: queryResult.refetch,
    handleParamsChange: handleParamsChange,
});
