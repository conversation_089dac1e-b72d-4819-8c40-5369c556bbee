import { renderHook, act } from "@testing-library/react";
import { useFormContext } from "react-hook-form";
import { useSnackbar } from "context/SnackbarProvider";
import { useRegisterEntryContext } from "register/context/RegisterEntryContext";
import { getFieldsByColumnType } from "register/utils/utils";
import { useFrsvEvaluateAllFormulasUsingPostMutation } from "common/api/formulas";
import { useCalculateAllFormulas } from "./useCalculateAllFormulas";

jest.mock("react-hook-form", () => ({
  useFormContext: jest.fn(),
}));

jest.mock("context/SnackbarProvider", () => ({
  useSnackbar: jest.fn(),
}));

jest.mock("register/context/RegisterEntryContext", () => ({
  useRegisterEntryContext: jest.fn(),
}));

jest.mock("common/api/formulas", () => ({
  useFrsvEvaluateAllFormulasUsingPostMutation: jest.fn(),
}));

jest.mock("common/utils/i18n", () => ({
  strings: jest.fn((k: string) => k),
}));

jest.mock("register/utils/utils", () => ({
  updateEntryRecord: jest.fn((r: any) => r),
  getFieldsByColumnType: jest.fn(),
}));

const buildRegister = () => ({
  sections: [
    {
      fields: [
        { columnName: "field1", label: "Field One", columnType: "SIMPLE_FORMULA" },
        { columnName: "field2", label: "Field Two", columnType: "COMPLEX_FORMULA" },
        {
          columnName: "dateField",
          label: "Date Field",
          columnType: "DATETIME_FORMULA",
        },
        {
          columnName: "stringField",
          label: "String Field",
          columnType: "STRING_FORMULA",
        },
      ],
    },
  ],
});

type ApiReject = { data?: { message?: string }; message?: string };

const mockReject = (err: ApiReject) => {
  (useFrsvEvaluateAllFormulasUsingPostMutation as jest.Mock).mockReturnValue([
    jest.fn().mockReturnValue({ unwrap: () => Promise.reject(err) }),
    { isLoading: false },
  ]);
};

const mockResolve = (result: any) => {
  (useFrsvEvaluateAllFormulasUsingPostMutation as jest.Mock).mockReturnValue([
    jest.fn().mockReturnValue({ unwrap: () => Promise.resolve(result) }),
    { isLoading: false },
  ]);
};

const mockGetValues = jest.fn();
const mockSetValue = jest.fn();
const mockClearErrors = jest.fn();
const mockSetError = jest.fn();
const mockEnqueueSnackbar = jest.fn();

beforeEach(() => {
  jest.clearAllMocks();

  (useFormContext as jest.Mock).mockReturnValue({
    getValues: mockGetValues,
    setValue: mockSetValue,
    clearErrors: mockClearErrors,
    setError: mockSetError,
  });

  (useSnackbar as jest.Mock).mockReturnValue({ enqueueSnackbar: mockEnqueueSnackbar });

  const register = buildRegister();
  (useRegisterEntryContext as jest.Mock).mockReturnValue({
    register,
    entry: { record: {} },
  });

  (getFieldsByColumnType as jest.Mock).mockImplementation((reg, ct) =>
    reg.sections[0].fields.filter((f: any) => f.columnType === ct)
  );

  mockResolve({ record: {} });

  mockGetValues.mockReturnValue({});
});

describe("useCalculateAllFormulas – extended suite", () => {
  it("calculates formulas successfully and updates fields", async () => {

    const backendRecord = {
      sections: [
        {
          fields: [
            { fieldName: "field1", simpleValue: ["42"] },
            { fieldName: "dateField", simpleValue: ["2025-01-01"] },
          ],
        },
      ],
    };

    mockResolve({ record: backendRecord });

    const { result } = renderHook(() => useCalculateAllFormulas());

    await act(async () => {
      const success = await result.current.calculateAllFormulas();
      expect(success).toBe(true);
    });

    // Snackbars appear twice: start + finish
    expect(mockEnqueueSnackbar).toHaveBeenNthCalledWith(
      1,
      "register:message.calculatingFormulas",
      { variant: "info" }
    );
    expect(mockEnqueueSnackbar).toHaveBeenNthCalledWith(
      2,
      "register:message.formulaCalculationComplete",
      { variant: "info" }
    );

    expect(mockSetValue).toHaveBeenCalledWith(
      "field1",
      "42",
      expect.objectContaining({ shouldDirty: true })
    );
    expect(mockSetValue).toHaveBeenCalledWith(
      "dateField",
      "2025-01-01",
      expect.objectContaining({ shouldDirty: true, shouldTouch: true })
    );

    expect(mockClearErrors).toHaveBeenCalledTimes(2);
    expect(mockClearErrors).toHaveBeenNthCalledWith(1, "field1");
    expect(mockClearErrors).toHaveBeenNthCalledWith(2, "dateField");
  });

  it("handles complex rawErrorMessage and sets field errors", async () => {
    const raw =
      "Main error, Failed to evaluate field[field1]Reason: Wrong, Failed to evaluate field[field2]Reason: Boom";
    mockReject({ data: { message: raw }, message: "API Error" });

    const { result } = renderHook(() => useCalculateAllFormulas());
    await act(async () => await result.current.calculateAllFormulas());

    ["field1", "field2", "dateField", "stringField"].forEach((f, i) =>
      expect(mockClearErrors).toHaveBeenNthCalledWith(i + 1, f)
    );

    expect(mockSetError).toHaveBeenCalledWith("field1", {
      type: "error",
      message:
        "common:formula.failed [Field One]. common:label.reason: Wrong",
    });
    expect(mockSetError).toHaveBeenCalledWith("field2", {
      type: "error",
      message: "common:formula.failed [Field Two]. common:label.reason: Boom",
    });

    expect(result.current.formulaErrors).toHaveLength(3);
    expect(result.current.showErrorDialog).toBe(true);
  });

  it("ignores unknown field names in rawErrorMessage", async () => {
    const raw = "Failed to evaluate field[unknownField]Reason: Kaboom";
    mockReject({ data: { message: raw } });

    const { result } = renderHook(() => useCalculateAllFormulas());
    await act(async () => await result.current.calculateAllFormulas());

    expect(mockSetError).not.toHaveBeenCalled();

    expect(result.current.formulaErrors[0].errorMessage).toContain("unknownField");
  });

  it("handles rawErrorMessage without Reason", async () => {
    const raw = "Failed to evaluate field[field1]";
    mockReject({ data: { message: raw } });

    const { result } = renderHook(() => useCalculateAllFormulas());
    await act(async () => await result.current.calculateAllFormulas());

    expect(result.current.formulaErrors[0].errorMessage).toContain("Failed to evaluate field");

    expect(mockSetError).not.toHaveBeenCalled();
  });

  it("handles malformed rawErrorMessage without brackets", async () => {
    const raw = "Failed to evaluate field field1 Reason: Oh no";
    mockReject({ data: { message: raw } });

    const { result } = renderHook(() => useCalculateAllFormulas());
    await act(async () => await result.current.calculateAllFormulas());

    expect(mockSetError).not.toHaveBeenCalled();
    expect(result.current.formulaErrors[0].errorMessage).toContain("Failed to evaluate field field1");
  });

  it("handles data.message only", async () => {
    const raw = "Error from data message only";
    mockReject({ data: { message: raw } });

    const { result } = renderHook(() => useCalculateAllFormulas());
    await act(async () => await result.current.calculateAllFormulas());

    expect(mockSetError).not.toHaveBeenCalled();
    expect(result.current.formulaErrors).toEqual([
      {
        fieldName: "formulas",
        fieldLabel: "common:label.error",
        errorMessage: `<b>Error: </b>${raw}`,
      },
    ]);
  });

  it("handles general message only", async () => {
    const raw = "Error from general message only";
    mockReject({ message: raw });

    const { result } = renderHook(() => useCalculateAllFormulas());
    await act(async () => await result.current.calculateAllFormulas());

    expect(mockSetError).not.toHaveBeenCalled();
    expect(result.current.formulaErrors[0].errorMessage).toContain(raw);
  });

  it("handles error object without rawErrorMessage", async () => {
    mockReject({});

    const { result } = renderHook(() => useCalculateAllFormulas());
    await act(async () => await result.current.calculateAllFormulas());

    expect(mockSetError).not.toHaveBeenCalled();
    expect(result.current.formulaErrors).toEqual([]);
    expect(result.current.showErrorDialog).toBe(true);
  });
});
