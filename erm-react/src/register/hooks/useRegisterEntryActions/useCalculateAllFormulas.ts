import { useCallback, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useRegisterEntryContext } from 'register/context/RegisterEntryContext';
import { updateEntryRecord, getFieldsByColumnType } from 'register/utils/utils';
import { ColumnType } from 'register/types';
import { useFrsvEvaluateAllFormulasUsingPostMutation } from 'common/api/formulas';
import { strings } from 'common/utils/i18n';
import { useSnackbar } from 'context/SnackbarProvider';

type FormulaError = {
    fieldName: string;
    fieldLabel: string;
    errorMessage: string;
};

export const useCalculateAllFormulas = () => {
    const { register, entry } = useRegisterEntryContext();
    const { getValues, setValue, clearErrors, setError } = useFormContext();
    const [evaluateAllFormulas, { isLoading }] = useFrsvEvaluateAllFormulasUsingPostMutation();
    const { enqueueSnackbar } = useSnackbar();

    const [formulaErrors, setFormulaErrors] = useState<FormulaError[]>([]);
    const [showErrorDialog, setShowErrorDialog] = useState(false);

    const calculateAllFormulas = useCallback(async (): Promise<boolean> => {
        if (!register || !entry) {
            return false;
        }

        const formulaFields = [
            ...getFieldsByColumnType(register, ColumnType.SIMPLE_FORMULA),
            ...getFieldsByColumnType(register, ColumnType.COMPLEX_FORMULA),
            ...getFieldsByColumnType(register, ColumnType.DATETIME_FORMULA),
            ...getFieldsByColumnType(register, ColumnType.STRING_FORMULA),
        ];

        try {
            setFormulaErrors([]);
            setShowErrorDialog(false);

            enqueueSnackbar(strings('register:message.calculatingFormulas'), { variant: 'info' });

            const formValues = getValues();

            const updatedRecord = updateEntryRecord(entry?.record, formValues);

            const result = await evaluateAllFormulas({
                registerDataRest: { record: updatedRecord },
            }).unwrap();

            const formulaFieldNames = new Set(formulaFields.map(f => f.columnName));
            const dateFormulaFieldNames = new Set(
                getFieldsByColumnType(register, ColumnType.DATETIME_FORMULA).map(f => f.columnName)
            );

            if (result.record?.sections) {
                for (const section of result.record.sections) {
                    if (section.fields) {
                        for (const field of section.fields) {
                            if (field.fieldName &&
                                formulaFieldNames.has(field.fieldName) &&
                                field.simpleValue &&
                                field.simpleValue.length > 0) {

                                const newValue = field.simpleValue[0];
                                const currentValue = getValues(field.fieldName);

                                if (newValue !== currentValue) {
                                    setValue(field.fieldName, newValue, { shouldDirty: true });

                                    if (dateFormulaFieldNames.has(field.fieldName) && newValue.trim() !== '') {
                                        setValue(field.fieldName, newValue, { shouldDirty: true, shouldTouch: true });
                                    }
                                }
                                clearErrors(field.fieldName);
                            }
                        }
                    }
                }
            }

            enqueueSnackbar(strings('register:message.formulaCalculationComplete'), { variant: 'info' });

            return true;
        } catch (error: any) {
            const rawErrorMessage = error?.data?.message || error?.message;

            formulaFields.forEach(field => {
                if (field.columnName) {
                    clearErrors(field.columnName);
                }
            });

            const errors: FormulaError[] = [];
            //TODO: We need to create a new endpoint on the backend so that this can be done more elegantly.
            // Until then, for testing purposes we’re using the internal endpoint.
            if (rawErrorMessage) {
                // Parse the error message to extract individual field errors
                const errorParts = rawErrorMessage.split(', Failed to evaluate field');

                if (errorParts.length > 0) {
                    // Handle the first error part
                    errors.push({
                        fieldName: 'formulas',
                        fieldLabel: strings('common:label.error'),
                        errorMessage: `<b>Error: </b>${errorParts[0]}`
                    });

                    // Handle subsequent field-specific errors
                    for (let i = 1; i < errorParts.length; i++) {
                        const errorText = `Failed to evaluate field${errorParts[i]}`;
                        errors.push({
                            fieldName: 'formulas',
                            fieldLabel: strings('common:label.error'),
                            errorMessage: `<b>Error: </b>${errorText}`
                        });

                        // Try to extract field name and reason from the error part
                        const fieldMatch = errorParts[i].match(/^\s*\[([^\]]+)\](.*)$/);
                        if (fieldMatch) {
                            const fieldName = fieldMatch[1];
                            let fieldReason = fieldMatch[2].trim();

                            // Clean up the reason by removing HTML tags and extra text
                            fieldReason = fieldReason.replace(/<[^>]*>/g, ''); // Remove HTML tags
                            fieldReason = fieldReason.replace(/^\.\s*/, ''); // Remove leading dot and spaces
                            fieldReason = fieldReason.replace(/^Reason:\s*/, ''); // Remove "Reason:" prefix if present
                            fieldReason = fieldReason.replace(/,$/, ''); // Remove trailing comma

                            // Find the field by label or column name
                            const field = formulaFields.find(f => f.label === fieldName || f.columnName === fieldName);

                            if (field && field.columnName) {
                                // Set individual field error with enhanced message format
                                const fieldErrorMessage = `${strings('common:formula.failed')} [${field.label || field.columnName}]. ${strings('common:label.reason')}: ${fieldReason}`;
                                setError(field.columnName, { type: 'error', message: fieldErrorMessage });
                            }
                        }
                    }
                }
            }

            setFormulaErrors(errors);
            setShowErrorDialog(true);
            return false;
        }
    }, [register, entry, getValues, setValue, clearErrors, setError, evaluateAllFormulas]);

    const closeErrorDialog = useCallback(() => {
        setShowErrorDialog(false);
        setFormulaErrors([]);
    }, []);

    return {
        calculateAllFormulas,
        isCalculating: isLoading,
        formulaErrors,
        showErrorDialog,
        closeErrorDialog,
    };
};
