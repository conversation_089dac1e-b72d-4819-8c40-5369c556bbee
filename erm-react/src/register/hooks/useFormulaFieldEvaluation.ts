import { useCallback, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useRegisterEntryContext } from 'register/context/RegisterEntryContext';
import { useFrsiEvaluateFormulaColumnUsingPostMutation } from 'common/api/formulas';
import { updateEntryRecord } from 'register/utils';
import { strings } from 'common/utils/i18n';

interface UseFormulaFieldEvaluationProps {
    columnName: string;
    fieldName: string;
    fieldLabel?: string;
    displayFormattingCallback: (value: string) => string;
    onEvaluationSuccess?: (value: string) => void;
}

export const useFormulaFieldEvaluation = ({
    columnName,
    fieldName,
    fieldLabel,
    displayFormattingCallback,
    onEvaluationSuccess,
}: UseFormulaFieldEvaluationProps) => {
    const { setValue, getValues, setError } = useFormContext();
    const { register, entry } = useRegisterEntryContext();
    const registerId = register?.id;
    const entryId = entry?.record?.id;
    const [evaluateFormulaColumn] = useFrsiEvaluateFormulaColumnUsingPostMutation();

    const [openDialog, setOpenDialog] = useState(false);
    const [errorReason, setErrorReason] = useState('');

    const evaluate = useCallback(async () => {
        if (registerId && entryId) {
            try {
                const formValues = getValues();
                const updatedRecord = updateEntryRecord(entry?.record, formValues);

                const response = await evaluateFormulaColumn({
                    colName: columnName,
                    registerDataRest: {
                        record: updatedRecord,
                    },
                }).unwrap();

                const simpleValue =
                    response?.record?.sections
                        ?.find((section) => section.fields?.some((f) => f.fieldName === columnName))
                        ?.fields?.find((f) => f.fieldName === columnName)?.simpleValue?.[0] ?? '';

                setValue(fieldName, simpleValue, { shouldValidate: true, shouldDirty: true });

                const formattedValue = displayFormattingCallback(simpleValue);
                if (onEvaluationSuccess) {
                    onEvaluationSuccess(formattedValue);
                }
            } catch (error: any) {
                const errorMessage = error.data.message;
                const failedMessageFirstLine = `${strings('common:formula.failed')} [${fieldLabel || fieldName}].`;
                const fullErrorMessage = `${failedMessageFirstLine}\n${strings('common:label.reason')}: ${errorMessage}`;
                setError(fieldName, { type: 'error', message: fullErrorMessage });
                setErrorReason(fullErrorMessage);
                setOpenDialog(true);
            }
        }
    }, [columnName, entry, evaluateFormulaColumn, fieldName, registerId, entryId, setValue, getValues, setError, fieldLabel, displayFormattingCallback, onEvaluationSuccess]);

    return { evaluate, openDialog, setOpenDialog, errorReason };
};
