import { SYSTEM_COLUMN } from 'common/types';
import { strings } from 'common/utils/i18n';
import { FilterType } from 'view/types';
import { ColumnType, EntryActionType } from 'register/types';

export const SHOW_UNSUPPORTED_ENTRY_ACTIONS = true; // Set to false to hide unsupported actions in the UI
export const SKIP_COMPARE_VALUE = '__SKIP_COMPARE__';

// TODO: add entry actions here if supported
export const SUPPORTED_ENTRY_ACTIONS: EntryActionType[] = [

    EntryActionType.SHARE,
    EntryActionType.CALCULATE_FORMULAS,
    EntryActionType.DELETE,
    EntryActionType.DUPLICATE];

export const NAME = 'register';

export const REGISTER_TEST_ID_PREFIX = 'register';

export const CORE_SECTION_LABEL = 'CORE';
export const MAIN_TAB_NAME = 'Main';
export const AUXILIARY_SUFFIX = '_aux';
export const AUXILIARY_COLUMN_0 = AUXILIARY_SUFFIX + '0';
export const AUXILIARY_COLUMN_1 = AUXILIARY_SUFFIX + '1';
export const AUXILIARY_COLUMN_2 = AUXILIARY_SUFFIX + '2';
export const AUXILIARY_COLUMN_3 = AUXILIARY_SUFFIX + '3';
export const AUXILIARY_COLUMN_PATTERN = '^col_[0-9]+_aux[0-9]$';
export const SECTION_APPLICATION = 'Section';
export const CORE_SECTION_IDENTITY = 1;

export const REGISTER_STATIC_FIELDS = {
    ID: {
        fieldName: SYSTEM_COLUMN.ID,
        label: strings('register:label.id'),
        filterType: FilterType.NUMBER,
    },
};

export const BUSINESS_UNIT = 'businessunit';

export const VIEW_EXCLUDED_COLUMN_TYPES = [ColumnType.HINT, ColumnType.LINKED_TO, ColumnType.SPACER, ColumnType.STATIC_TEXT, ColumnType.WORKLOG];

// Register form spacings
export const OUTER_SPACING_HORIZONTAL_SM = 0;
export const OUTER_SPACING_VERTICAL_SM = 12;
export const INNER_SPACING_HORIZONTAL_SM = 16;
export const INNER_SPACING_VERTICAL_SM = 20;

export const OUTER_SPACING_HORIZONTAL_MD = 24;
export const OUTER_SPACING_VERTICAL_MD = 20;
export const INNER_SPACING_HORIZONTAL_MD = 24;
export const INNER_SPACING_VERTICAL_MD = 20;

export const FIELDS_SPACING = 20;

export const DEBOUNCE_CONDITIONAL_RULES = 200;

export const PREFETCH_INFO_BUBBLE_THRESHOLD = 60;
