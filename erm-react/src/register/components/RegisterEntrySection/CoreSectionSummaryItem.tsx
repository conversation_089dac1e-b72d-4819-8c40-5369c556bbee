import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import useIntersectionObserver from 'common/hooks/useIntersectionObserver';
import React, { useLayoutEffect } from 'react';


interface CoreSectionSummaryItemProps {
  label: string;
  value: string;
  priority?: number;
  reducedSpacing?: boolean;
}

const CoreSectionSummaryItem: React.FC<CoreSectionSummaryItemProps> = ({
  label,
  value,
  priority = 0,
  reducedSpacing = false,
}) => {
  const [hideBelow, setHideBelow] = React.useState<number | null>(null);

  const { intersectionObserver, intersectedElement } = useIntersectionObserver({
    root: null,
    rootMargin: '0px',
    threshold: 0.1,
  });

  useLayoutEffect(() => {
    if (!intersectedElement?.target) {
  return;
  }

    const item = intersectedElement.target as HTMLElement;
    const container = item.parentElement as HTMLElement | null;
    if (!container) {
      return;
    }

    const measure = () => {

      if (item.offsetParent === null) {
        return;
      }

      const itemRect = item.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      const rightEdge = Math.ceil(itemRect.right - containerRect.left);

      if (rightEdge !== 0 && rightEdge !== hideBelow) {
        setHideBelow(rightEdge);
      }
    };

    measure();
  }, [intersectedElement, hideBelow]);

  const ref = React.useCallback((node: HTMLDivElement | null) => {
    if (node) {
      intersectionObserver(node);
    }
  }, [intersectionObserver]);

  const itemSx = React.useMemo(() => {
    const base = {
      display: 'flex',
      gap: '4px',
      alignItems: 'center',
      minWidth: 0,
      flexShrink: 0,
      whiteSpace: 'nowrap',
      marginRight: reducedSpacing ? '-12px' : '0px',
    } as const;

    if (hideBelow !== null) {
      return {
        ...base,
        [`@container (max-width: ${hideBelow - 1}px)`]: {
          display: 'none',
        },
      } as const;
    }

    return base;
  }, [hideBelow, reducedSpacing]);

  return (
    <Box ref={ref} sx={itemSx} data-priority={priority}>
      <Typography
        variant="body1"
        color="protechtGrey.grey_128"
        sx={{ flexShrink: 0, whiteSpace: 'nowrap' }}
      >
        {label}
      </Typography>
      <Typography
        variant="body2"
        sx={{
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          minWidth: 0,
        }}
      >
        {value}
      </Typography>
    </Box>
  );
};

export default CoreSectionSummaryItem;
