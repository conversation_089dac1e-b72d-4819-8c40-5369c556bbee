import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import React from 'react';
import CoreSectionSummaryItem from './CoreSectionSummaryItem';

type Props = {
    summaryValues: { label: string; value: string; noGap?: boolean }[];
};

const CoreSectionSummary: React.FC<Props> = ({ summaryValues }) => {
    return (
        <Grid item sx={{ minWidth: 0, flex: 1 }}>
            <Box
                sx={{
                    display: 'flex',
                    gap: '18px',
                    flexWrap: 'nowrap',
                    overflow: 'hidden',
                    alignItems: 'center',
                    minWidth: 0,
                    width: '100%',
                    containerType: 'inline-size',
                }}
            >
                {summaryValues?.map(({ value, label }, index) => {
                    const nextItemNoGap = index < summaryValues.length - 1 && summaryValues[index + 1]?.noGap;

                    return (
                        <CoreSectionSummaryItem
                            key={index}
                            label={label}
                            value={value ?? '-'}
                            priority={index}
                            reducedSpacing={nextItemNoGap}
                        />
                    );
                })}
            </Box>
        </Grid>
    );
};

export default CoreSectionSummary;
