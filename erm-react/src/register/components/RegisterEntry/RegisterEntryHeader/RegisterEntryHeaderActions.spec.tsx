import React from 'react';
import { render, screen } from 'test/utils';
import RegisterEntryHeaderActions from './RegisterEntryHeaderActions';
import { useRegisterEntryContext } from 'register/context/RegisterEntryContext';
import useFormContext from 'common/hooks/forms/useFormContext';
import useRegisterEntryActions from 'register/hooks/useRegisterEntryActions/useRegisterEntryActions';
import { EntryActionType, ScreenSize } from 'register/types';
import { waitFor, within } from '@testing-library/react';

const mockEnqueueSuccess = jest.fn();
const mockEnqueueError = jest.fn();
const mockEnqueueInfo = jest.fn();

jest.mock('common/hooks/useSnackbar', () => {
    return jest.fn(() => ({
        enqueueSuccess: mockEnqueueSuccess,
        enqueueError: mockEnqueueError,
        enqueueInfo: mockEnqueueInfo,
    }));
});

const mockNavigate = jest.fn();
jest.mock('react-router', () => ({
    ...jest.requireActual('react-router'),
    useNavigate: () => mockNavigate,
}));

jest.mock('register/context/RegisterEntryContext');
jest.mock('common/hooks/forms/useFormContext');
jest.mock('register/hooks/useRegisterEntryActions/useRegisterEntryActions');
jest.mock('register/hooks/useRegisterEntryActions/useCalculateAllFormulas');

const mockDeleteFn = jest.fn().mockImplementation(() => ({
    unwrap: () => Promise.resolve(),
}));

const mockDuplicateFn = jest.fn().mockImplementation(() => ({
    unwrap: () => Promise.resolve({ record: { id: 'new-entry-id' } }),
}));

jest.mock('register/rtkApi', () => ({
    ...jest.requireActual('register/rtkApi'),
    useRdrsvDeleteRegisterEntryUsingDeleteMutation: () => [mockDeleteFn, { isLoading: false }],
    useRdrsvCreateEntryCopyUsingPostMutation: () => [mockDuplicateFn, { isLoading: false }],
}));

const mockCalculateAllFormulas = jest.fn();

jest.mock('register/hooks/useRegisterEntryActions/useCalculateAllFormulas', () => ({
    useCalculateAllFormulas: () => ({
        calculateAllFormulas: mockCalculateAllFormulas,
        isCalculating: false,
        formulaErrors: [],
        showErrorDialog: false,
        closeErrorDialog: jest.fn(),
    }),
}));

jest.mock('common/utils/i18n', () => ({
    strings: jest.fn((key) => {
        if (key === 'common:button.save') return 'Save';
        if (key === 'common:button.submit') return 'Submit';
        if (key === 'common:button.cancel') return 'Cancel';
        if (key === 'common:button.delete') return 'Delete';
        if (key === 'common:button.duplicate') return 'Duplicate';
        if (key === 'common:message.unsavedChangesDiscard') return 'You have unsaved changes. Discard these changes?';
        if (key === 'register:title.deleteRegisterEntry') return 'Delete Entry';
        if (key === 'register:message.deleteRegisterEntryConfirmation') return 'Are you sure you want to delete this entry?';
        if (key === 'register:message.registerEntryDeleted') return 'Entry deleted successfully';
        if (key === 'register:error.deleteEntry') return 'Failed to delete entry';
        if (key === 'register:title.duplicateRegisterEntry') return 'Duplicate Entry';
        if (key === 'register:message.duplicateRegisterEntryConfirmation') return 'Do you want to duplicate this item, copying all appropriate field values?';
        if (key === 'register:message.registerEntryDuplicatedDetailed') return 'Item has been duplicated. You are now viewing the copy of the original.';
        if (key === 'register:error.duplicateEntry') return 'Failed to duplicate entry';
        if (key === 'register:message.copyLinkSuccess') return 'A link to this item has been copied to the clipboard.';
        return key;
    }),
}));

describe('RegisterEntryHeaderActions', () => {
    const mockOnNavigateBack = jest.fn();
    const mockOnSave = jest.fn();
    const mockOnCancel = jest.fn();

    const mockRegister = { id: 1, tableName: 'testTable' };
    const mockEntry = {
        id: 'entry1',
        editableByCurrentUser: true,
        record: { id: 'record1' },
    };
    const mockFormState = { isDirty: true, isValid: true };

    const mockDeleteAction = {
        key: EntryActionType.DELETE,
        label: 'Delete',
        icon: () => <div data-testid="delete-icon" />,
        isDisabled: () => false,
        color: 'error.main',
    };

    const mockShareAction = {
        key: EntryActionType.SHARE,
        label: 'Share',
        icon: () => <div data-testid="share-icon" />,
        isDisabled: () => false,
    };

    const mockMainActions = [mockDeleteAction, mockShareAction];

    const mockMoreActions = [
        {
            key: EntryActionType.HISTORY,
            label: 'History',
            icon: () => <div data-testid="history-icon" />,
            isDisabled: () => false,
        },
        {
            key: EntryActionType.DUPLICATE,
            label: 'Duplicate',
            icon: () => <div data-testid="duplicate-icon" />,
            isDisabled: () => true,
        },
    ];

    beforeEach(() => {
        jest.clearAllMocks();
        mockCalculateAllFormulas.mockClear();
        mockCalculateAllFormulas.mockResolvedValue(true);

        (useRegisterEntryContext as jest.Mock).mockReturnValue({
            register: mockRegister,
            isAnonymous: false,
            entry: mockEntry,
            isReadOnly: false,
            screenSize: ScreenSize.XL,
            isSubEntry: false,
        });
        (useFormContext as jest.Mock).mockReturnValue({
            formState: mockFormState,
            reset: jest.fn(),
        });
        (useRegisterEntryActions as jest.Mock).mockReturnValue([...mockMainActions, ...mockMoreActions]);
    });

    it('renders cancel button when not anonymous', () => {
        render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        expect(screen.getByTestId('registerEntryToolbar-cancelButton')).toBeInTheDocument();
        expect(screen.getByText('Cancel')).toBeInTheDocument();
    });

    it('renders save button', () => {
        render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        expect(screen.getByTestId('registerEntryToolbar-saveButton')).toBeInTheDocument();
        expect(screen.getByText('Save')).toBeInTheDocument();
    });

    it('displays "Save" label when not anonymous', () => {
        (useRegisterEntryContext as jest.Mock).mockReturnValue({
            register: mockRegister,
            isAnonymous: false,
            entry: mockEntry,
            isReadOnly: false,
            screenSize: ScreenSize.XL,
            isSubEntry: false,
        });

        render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        expect(screen.getByText('Save')).toBeInTheDocument();
    });

    it('displays "Submit" label when anonymous and not subentry', () => {
        (useRegisterEntryContext as jest.Mock).mockReturnValue({
            register: mockRegister,
            isAnonymous: true,
            entry: mockEntry,
            isReadOnly: false,
            screenSize: ScreenSize.XL,
            isSubEntry: false,
        });

        render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        expect(screen.getByText('Submit')).toBeInTheDocument();
    });

    it('does not render cancel button when anonymous and onCancel is not provided', () => {
        (useRegisterEntryContext as jest.Mock).mockReturnValue({
            register: mockRegister,
            isAnonymous: true,
            entry: mockEntry,
            isReadOnly: false,
            screenSize: ScreenSize.XL,
            isSubEntry: false,
        });

        render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        expect(screen.queryByTestId('registerEntryToolbar-cancelButton')).not.toBeInTheDocument();
    });

    it('renders cancel button when anonymous but onCancel is provided', () => {
        (useRegisterEntryContext as jest.Mock).mockReturnValue({
            register: mockRegister,
            isAnonymous: true,
            entry: mockEntry,
            isReadOnly: false,
            screenSize: ScreenSize.XL,
            isSubEntry: false,
        });

        render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
                onCancel={mockOnCancel}
            />,
        );

        expect(screen.getByTestId('registerEntryToolbar-cancelButton')).toBeInTheDocument();
    });

    it('disables save button when conditions are not met', () => {
        (useRegisterEntryContext as jest.Mock).mockReturnValue({
            register: mockRegister,
            isAnonymous: false,
            entry: { ...mockEntry, editableByCurrentUser: false },
            isReadOnly: false,
            screenSize: ScreenSize.XL,
            isSubEntry: false,
        });

        render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        const saveButton = screen.getByTestId('registerEntryToolbar-saveButton');
        expect(saveButton).toBeDisabled();
    });

    it('disables save button when form is not dirty and not anonymous', () => {
        (useFormContext as jest.Mock).mockReturnValue({
            formState: { isDirty: false, isValid: true },
            reset: jest.fn(),
        });

        render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        const saveButton = screen.getByTestId('registerEntryToolbar-saveButton');
        expect(saveButton).toBeDisabled();
    });

    it('disables save button when form is not valid', () => {
        (useFormContext as jest.Mock).mockReturnValue({
            formState: { isDirty: true, isValid: false },
            reset: jest.fn(),
        });

        render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        const saveButton = screen.getByTestId('registerEntryToolbar-saveButton');
        expect(saveButton).toBeDisabled();
    });

    it('disables save button when isReadOnly is true', () => {
        (useRegisterEntryContext as jest.Mock).mockReturnValue({
            register: mockRegister,
            isAnonymous: false,
            entry: mockEntry,
            isReadOnly: true,
            screenSize: ScreenSize.XL,
            isSubEntry: false,
        });

        render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        const saveButton = screen.getByTestId('registerEntryToolbar-saveButton');
        expect(saveButton).toBeDisabled();
    });

    it('save button is disabled when editing entry and editableByUser is false', () => {
        (useRegisterEntryContext as jest.Mock).mockReturnValue({
            register: mockRegister,
            isAnonymous: false,
            entry: { ...mockEntry, editableByCurrentUser: false },
            isReadOnly: false,
            screenSize: ScreenSize.XL,
            isSubEntry: false,
            isCreate: false,
        });

        render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        const saveButton = screen.getByTestId('registerEntryToolbar-saveButton');
        expect(saveButton).toBeDisabled();
    });

    it('save button is enabled when editing entry and editableByCurrentUser is true', () => {
        (useRegisterEntryContext as jest.Mock).mockReturnValue({
            register: mockRegister,
            isAnonymous: false,
            entry: { ...mockEntry, editableByCurrentUser: true },
            isReadOnly: false,
            screenSize: ScreenSize.XL,
            isSubEntry: false,
            isCreate: false,
        });

        render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        const saveButton = screen.getByTestId('registerEntryToolbar-saveButton');
        expect(saveButton).toBeEnabled();
    });

    it('save button is enabled during create operation even when editableByCurrentUser is false', () => {
        (useRegisterEntryContext as jest.Mock).mockReturnValue({
            register: mockRegister,
            isAnonymous: false,
            entry: { ...mockEntry, editableByCurrentUser: false },
            isReadOnly: false,
            screenSize: ScreenSize.XL,
            isSubEntry: false,
            isCreate: true,
        });

        render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        const saveButton = screen.getByTestId('registerEntryToolbar-saveButton');
        expect(saveButton).toBeEnabled();
    });

    it('save button is enabled during create operation when editableByCurrentUser is true', () => {
        (useRegisterEntryContext as jest.Mock).mockReturnValue({
            register: mockRegister,
            isAnonymous: false,
            entry: { ...mockEntry, editableByCurrentUser: true },
            isReadOnly: false,
            screenSize: ScreenSize.XL,
            isSubEntry: false,
            isCreate: true,
        });

        render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        const saveButton = screen.getByTestId('registerEntryToolbar-saveButton');
        expect(saveButton).toBeEnabled();
    });

    it('hides action button labels on smaller screens', () => {
        (useRegisterEntryContext as jest.Mock).mockReturnValue({
            register: mockRegister,
            isAnonymous: false,
            entry: mockEntry,
            isReadOnly: false,
            screenSize: ScreenSize.MD,
            isSubEntry: false,
        });

        render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        // Main action buttons should be rendered but with hidden labels
        const actionButtons = screen.getAllByTestId(/registerEntryToolbar-actionButton/);
        expect(actionButtons.length).toBeGreaterThan(0);
    });

    it('calls onNavigateBack when cancel button is clicked', () => {
        render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        const cancelButton = screen.getByTestId('registerEntryToolbar-cancelButton');
        cancelButton.click();

        expect(mockOnNavigateBack).toHaveBeenCalledTimes(1);
    });

    it('calls onSave when save button is clicked', () => {
        render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        const saveButton = screen.getByTestId('registerEntryToolbar-saveButton');
        saveButton.click();

        expect(mockOnSave).toHaveBeenCalledTimes(1);
    });

    it('calculates formulas before saving in anonymous mode', async () => {
        (useRegisterEntryContext as jest.Mock).mockReturnValue({
            register: mockRegister,
            isAnonymous: true,
            entry: mockEntry,
            isReadOnly: false,
            screenSize: ScreenSize.XL,
            isSubEntry: false,
        });

        const { user } = render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        const saveButton = screen.getByTestId('registerEntryToolbar-saveButton');
        await user.click(saveButton);

        expect(mockCalculateAllFormulas).toHaveBeenCalledTimes(1);
        expect(mockOnSave).toHaveBeenCalledTimes(1);
    });

    it('does not calculate formulas before saving in non-anonymous mode', () => {
        render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        const saveButton = screen.getByTestId('registerEntryToolbar-saveButton');
        saveButton.click();

        expect(mockCalculateAllFormulas).not.toHaveBeenCalled();
        expect(mockOnSave).toHaveBeenCalledTimes(1);
    });

    it('does not save when formula calculation fails in anonymous mode', async () => {
        (useRegisterEntryContext as jest.Mock).mockReturnValue({
            register: mockRegister,
            isAnonymous: true,
            entry: mockEntry,
            isReadOnly: false,
            screenSize: ScreenSize.XL,
            isSubEntry: false,
        });

        mockCalculateAllFormulas.mockResolvedValue(false);

        const { user } = render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        const saveButton = screen.getByTestId('registerEntryToolbar-saveButton');
        await user.click(saveButton);

        await waitFor(() => {
            expect(mockCalculateAllFormulas).toHaveBeenCalledTimes(1);
            expect(mockOnSave).not.toHaveBeenCalled();
        });
    });

    it('saves when formula calculation succeeds in anonymous mode', async () => {
        (useRegisterEntryContext as jest.Mock).mockReturnValue({
            register: mockRegister,
            isAnonymous: true,
            entry: mockEntry,
            isReadOnly: false,
            screenSize: ScreenSize.XL,
            isSubEntry: false,
        });

        mockCalculateAllFormulas.mockResolvedValue(true);

        const { user } = render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        const saveButton = screen.getByTestId('registerEntryToolbar-saveButton');
        await user.click(saveButton);

        await waitFor(() => {
            expect(mockCalculateAllFormulas).toHaveBeenCalledTimes(1);
            expect(mockOnSave).toHaveBeenCalledTimes(1);
        });
    });

    it('applies isDisabled function to main action buttons', () => {
        const mockActionsWithDisabled = [
            {
                ...mockMainActions[0],
                isDisabled: () => true,
            },
            mockMainActions[1],
        ];

        (useRegisterEntryActions as jest.Mock).mockReturnValue([...mockActionsWithDisabled, ...mockMoreActions]);

        render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        const deleteButton = screen.getByTestId('registerEntryToolbar-actionButton-DELETE');
        expect(deleteButton).toBeDisabled();

        const shareButton = screen.getByTestId('registerEntryToolbar-actionButton-SHARE');
        expect(shareButton).toBeEnabled();
    });

    it('passes isDisabled function arguments correctly', () => {
        const mockIsDisabled = jest.fn(() => false);
        const mockActionsWithSpy = [
            {
                ...mockMainActions[0],
                isDisabled: mockIsDisabled,
            },
            mockMainActions[1],
        ];

        (useRegisterEntryActions as jest.Mock).mockReturnValue([...mockActionsWithSpy, ...mockMoreActions]);

        render(
            <RegisterEntryHeaderActions
                onNavigateBack={mockOnNavigateBack}
                onSave={mockOnSave}
            />,
        );

        expect(mockIsDisabled).toHaveBeenCalledWith(mockRegister, mockEntry, false, false);
    });

    describe('action buttons and more dropdown', () => {
        it('shows all actions as buttons when count equals MAX_MAIN_ACTIONS (2 actions)', () => {
            // Mock exactly 2 actions (equals MAX_MAIN_ACTIONS)
            const twoActions = mockMainActions.slice(0, 2);
            (useRegisterEntryActions as jest.Mock).mockReturnValue(twoActions);

            render(
                <RegisterEntryHeaderActions
                    onNavigateBack={mockOnNavigateBack}
                    onSave={mockOnSave}
                />,
            );

            expect(screen.getByTestId('registerEntryToolbar-actionButton-DELETE')).toBeInTheDocument();
            expect(screen.getByTestId('registerEntryToolbar-actionButton-SHARE')).toBeInTheDocument();

            expect(screen.queryByTestId('registerEntryToolbar-moreActionsDropdown')).not.toBeInTheDocument();
        });

        it('shows all actions as buttons when count is MAX_MAIN_ACTIONS + 1 (3 actions) to avoid single item dropdown', () => {
            // Mock exactly 3 actions (MAX_MAIN_ACTIONS + 1)
            const threeActions = [...mockMainActions, mockMoreActions[0]];
            (useRegisterEntryActions as jest.Mock).mockReturnValue(threeActions);

            render(
                <RegisterEntryHeaderActions
                    onNavigateBack={mockOnNavigateBack}
                    onSave={mockOnSave}
                />,
            );

            expect(screen.getByTestId('registerEntryToolbar-actionButton-DELETE')).toBeInTheDocument();
            expect(screen.getByTestId('registerEntryToolbar-actionButton-SHARE')).toBeInTheDocument();
            expect(screen.getByTestId('registerEntryToolbar-actionButton-HISTORY')).toBeInTheDocument();

            expect(screen.queryByTestId('registerEntryToolbar-moreActionsDropdown')).not.toBeInTheDocument();
        });

        it('shows buttons and dropdown when count is greater than MAX_MAIN_ACTIONS + 1 (4+ actions)', () => {
            // Mock 4 actions (MAX_MAIN_ACTIONS + 2)
            const fourActions = [...mockMainActions, ...mockMoreActions];
            (useRegisterEntryActions as jest.Mock).mockReturnValue(fourActions);

            render(
                <RegisterEntryHeaderActions
                    onNavigateBack={mockOnNavigateBack}
                    onSave={mockOnSave}
                />,
            );

            // Should show first 2 actions as buttons (MAX_MAIN_ACTIONS)
            expect(screen.getByTestId('registerEntryToolbar-actionButton-DELETE')).toBeInTheDocument();
            expect(screen.getByTestId('registerEntryToolbar-actionButton-SHARE')).toBeInTheDocument();

            // Should not show additional actions as buttons
            expect(screen.queryByTestId('registerEntryToolbar-actionButton-HISTORY')).not.toBeInTheDocument();
            expect(screen.queryByTestId('registerEntryToolbar-actionButton-DUPLICATE')).not.toBeInTheDocument();

            // Should show dropdown with remaining actions
            expect(screen.getByTestId('registerEntryToolbar-moreActionsDropdown')).toBeInTheDocument();
        });

        it('shows no actions when there are 0 actions', () => {
            (useRegisterEntryActions as jest.Mock).mockReturnValue([]);

            render(
                <RegisterEntryHeaderActions
                    onNavigateBack={mockOnNavigateBack}
                    onSave={mockOnSave}
                />,
            );

            expect(screen.queryByTestId(/registerEntryToolbar-actionButton/)).not.toBeInTheDocument();

            expect(screen.queryByTestId('registerEntryToolbar-moreActionsDropdown')).not.toBeInTheDocument();
        });

        it('shows no actions when in anonymous mode', () => {
            (useRegisterEntryContext as jest.Mock).mockReturnValue({
                register: mockRegister,
                isAnonymous: true,
                entry: mockEntry,
                isReadOnly: false,
                screenSize: ScreenSize.XL,
                isSubEntry: false,
            });

            (useRegisterEntryActions as jest.Mock).mockReturnValue([]);

            render(
                <RegisterEntryHeaderActions
                    onNavigateBack={mockOnNavigateBack}
                    onSave={mockOnSave}
                />,
            );

            expect(screen.queryByTestId(/registerEntryToolbar-actionButton-/)).not.toBeInTheDocument();
            expect(screen.queryByTestId('registerEntryToolbar-moreActionsDropdown')).not.toBeInTheDocument();
        });

        it('shows single action as button when there is 1 action', () => {
            // Mock exactly 1 action
            const oneAction = [mockMainActions[0]];
            (useRegisterEntryActions as jest.Mock).mockReturnValue(oneAction);

            render(
                <RegisterEntryHeaderActions
                    onNavigateBack={mockOnNavigateBack}
                    onSave={mockOnSave}
                />,
            );

            expect(screen.getByTestId('registerEntryToolbar-actionButton-DELETE')).toBeInTheDocument();

            expect(screen.queryByTestId('registerEntryToolbar-moreActionsDropdown')).not.toBeInTheDocument();
        });
    });

    describe('action handlers', () => {
        describe('Delete Action', () => {
            describe('confirmation dialog flow', () => {
                it('shows confirmation dialog when delete button is clicked', async () => {
                    (useRegisterEntryActions as jest.Mock).mockReturnValue([mockDeleteAction]);

                    const { user } = render(
                        <RegisterEntryHeaderActions
                            onNavigateBack={mockOnNavigateBack}
                            onSave={mockOnSave}
                        />,
                    );

                    const deleteButton = screen.getByTestId('registerEntryToolbar-actionButton-DELETE');
                    await user.click(deleteButton);

                    expect(screen.getByText('Delete Entry')).toBeInTheDocument();
                });

                it('closes dialog when user cancels', async () => {
                    (useRegisterEntryActions as jest.Mock).mockReturnValue([mockDeleteAction]);

                    const { user } = render(
                        <RegisterEntryHeaderActions
                            onNavigateBack={mockOnNavigateBack}
                            onSave={mockOnSave}
                        />,
                    );

                    const deleteButton = screen.getByTestId('registerEntryToolbar-actionButton-DELETE');
                    await user.click(deleteButton);

                    const dialogTitle = screen.getByText('Delete Entry');
                    expect(dialogTitle).toBeInTheDocument();

                    const dialog = screen.getByRole('dialog');

                    const cancelButton = within(dialog).getByText('Cancel');
                    await user.click(cancelButton);

                    expect(dialogTitle).not.toBeInTheDocument();
                });
            });

            describe('successful deletion', () => {
                it('displays success message when deletion is confirmed and succeeds', async () => {
                    (useRegisterEntryActions as jest.Mock).mockReturnValue([mockDeleteAction]);

                    const { user } = render(
                        <RegisterEntryHeaderActions
                            onNavigateBack={mockOnNavigateBack}
                            onSave={mockOnSave}
                        />,
                    );

                    const deleteButton = screen.getByTestId('registerEntryToolbar-actionButton-DELETE');
                    await user.click(deleteButton);

                    const dialog = screen.getByRole('dialog');
                    const confirmButton = within(dialog).getByText('Delete');
                    await user.click(confirmButton);

                    expect(mockDeleteFn).toHaveBeenCalledWith({
                        regId: mockRegister.id,
                        entryId: mockEntry.record.id,
                    });

                    await waitFor(() => expect(mockEnqueueSuccess).toHaveBeenCalled());
                });

                it('navigates to entries list after successful deletion', async () => {
                    (useRegisterEntryActions as jest.Mock).mockReturnValue([mockDeleteAction]);

                    const { user } = render(
                        <RegisterEntryHeaderActions
                            onNavigateBack={mockOnNavigateBack}
                            onSave={mockOnSave}
                        />,
                    );

                    const deleteButton = screen.getByTestId('registerEntryToolbar-actionButton-DELETE');
                    await user.click(deleteButton);

                    const dialog = screen.getByRole('dialog');
                    const confirmButton = within(dialog).getByText('Delete');
                    await user.click(confirmButton);

                    await waitFor(() => expect(mockNavigate).toHaveBeenCalledWith(`/registers/${mockRegister.tableName}/entries`));
                });

                it('closes dialog after successful deletion', async () => {
                    (useRegisterEntryActions as jest.Mock).mockReturnValue([mockDeleteAction]);
                    const { user } = render(
                        <RegisterEntryHeaderActions
                            onNavigateBack={mockOnNavigateBack}
                            onSave={mockOnSave}
                        />,
                    );
                    const deleteButton = screen.getByTestId('registerEntryToolbar-actionButton-DELETE');
                    await user.click(deleteButton);
                    const dialog = screen.getByRole('dialog');
                    const confirmButton = within(dialog).getByText('Delete');
                    await user.click(confirmButton);
                    await waitFor(() => expect(dialog).not.toBeInTheDocument());
                });
            });

            describe('error handling', () => {
                it('displays an error message in case of unsuccessful deletion', async () => {
                    mockDeleteFn.mockImplementationOnce(() => {
                        return {
                            unwrap: () => Promise.reject(new Error('Deletion failed')),
                        };
                    });

                    (useRegisterEntryActions as jest.Mock).mockReturnValue([mockDeleteAction]);

                    const { user } = render(
                        <RegisterEntryHeaderActions
                            onNavigateBack={mockOnNavigateBack}
                            onSave={mockOnSave}
                        />,
                    );

                    const deleteButton = screen.getByTestId('registerEntryToolbar-actionButton-DELETE');
                    await user.click(deleteButton);

                    const dialog = screen.getByRole('dialog');
                    const confirmButton = within(dialog).getByText('Delete');
                    await user.click(confirmButton);

                    await waitFor(() => expect(mockEnqueueError).toHaveBeenCalled());
                });

                it('keeps dialog open when deletion fails and does not navigate to register entries list', async () => {
                    mockDeleteFn.mockImplementationOnce(() => {
                        return {
                            unwrap: () => Promise.reject(new Error('Deletion failed')),
                        };
                    });

                    const { user } = render(
                        <RegisterEntryHeaderActions
                            onNavigateBack={mockOnNavigateBack}
                            onSave={mockOnSave}
                        />,
                    );

                    const deleteButton = screen.getByTestId('registerEntryToolbar-actionButton-DELETE');
                    await user.click(deleteButton);
                    const dialog = screen.getByRole('dialog');
                    const confirmButton = within(dialog).getByText('Delete');
                    await user.click(confirmButton);
                    expect(dialog).toBeInTheDocument();
                    expect(mockNavigate).not.toHaveBeenCalled();
                });
            });

            describe('loading state', () => {
                it('fires delete request only once when clicking confirm button multiple times', async () => {
                    (useRegisterEntryActions as jest.Mock).mockReturnValue([mockDeleteAction]);

                    const { user } = render(
                        <RegisterEntryHeaderActions
                            onNavigateBack={mockOnNavigateBack}
                            onSave={mockOnSave}
                        />,
                    );

                    const deleteButton = screen.getByTestId('registerEntryToolbar-actionButton-DELETE');
                    await user.click(deleteButton);
                    const dialog = screen.getByRole('dialog');
                    const confirmButton = within(dialog).getByText('Delete');
                    await user.click(confirmButton);
                    await user.click(confirmButton);
                    await user.click(confirmButton);

                    await waitFor(() => expect(mockDeleteFn).toHaveBeenCalledTimes(1));
                });
            });

            describe('Duplicate Action', () => {
                const mockDuplicateAction = {
                    key: EntryActionType.DUPLICATE,
                    label: 'Duplicate',
                    icon: () => <div data-testid="duplicate-icon" />,
                    isDisabled: () => false,
                    color: undefined,
                };

                describe('confirmation dialog flow', () => {
                    it('shows confirmation dialog when duplicate button is clicked', async () => {
                        // Set form as clean to bypass unsaved changes dialog
                        (useFormContext as jest.Mock).mockReturnValue({
                            formState: { isDirty: false, isValid: true },
                            reset: jest.fn(),
                        });

                        (useRegisterEntryActions as jest.Mock).mockReturnValue([mockDuplicateAction]);

                        const { user } = render(
                            <RegisterEntryHeaderActions
                                onNavigateBack={mockOnNavigateBack}
                                onSave={mockOnSave}
                            />,
                        );

                        const duplicateButton = screen.getByTestId('registerEntryToolbar-actionButton-DUPLICATE');
                        await user.click(duplicateButton);

                        expect(screen.getByText('Duplicate Entry')).toBeInTheDocument();
                    });

                    it('closes dialog when user cancels', async () => {
                        // Set form as clean to bypass unsaved changes dialog
                        (useFormContext as jest.Mock).mockReturnValue({
                            formState: { isDirty: false, isValid: true },
                            reset: jest.fn(),
                        });

                        (useRegisterEntryActions as jest.Mock).mockReturnValue([mockDuplicateAction]);

                        const { user } = render(
                            <RegisterEntryHeaderActions
                                onNavigateBack={mockOnNavigateBack}
                                onSave={mockOnSave}
                            />,
                        );

                        const duplicateButton = screen.getByTestId('registerEntryToolbar-actionButton-DUPLICATE');
                        await user.click(duplicateButton);

                        const dialogTitle = screen.getByText('Duplicate Entry');
                        expect(dialogTitle).toBeInTheDocument();

                        const dialog = screen.getByRole('dialog');

                        const cancelButton = within(dialog).getByText('Cancel');
                        await user.click(cancelButton);

                        expect(dialogTitle).not.toBeInTheDocument();
                    });
                });

                describe('successful duplication', () => {
                    it('displays info message when duplication is confirmed and succeeds', async () => {
                        // Set form as clean to bypass unsaved changes dialog
                        (useFormContext as jest.Mock).mockReturnValue({
                            formState: { isDirty: false, isValid: true },
                            reset: jest.fn(),
                        });

                        (useRegisterEntryActions as jest.Mock).mockReturnValue([mockDuplicateAction]);

                        const { user } = render(
                            <RegisterEntryHeaderActions
                                onNavigateBack={mockOnNavigateBack}
                                onSave={mockOnSave}
                            />,
                        );

                        const duplicateButton = screen.getByTestId('registerEntryToolbar-actionButton-DUPLICATE');
                        await user.click(duplicateButton);

                        const dialog = screen.getByRole('dialog');
                        const confirmButton = within(dialog).getByTestId('button-confirm');
                        await user.click(confirmButton);

                        expect(mockDuplicateFn).toHaveBeenCalledWith({
                            regId: mockRegister.id,
                            entryId: mockEntry.record.id,
                        });

                        await waitFor(() =>
                            expect(mockEnqueueInfo).toHaveBeenCalledWith('Item has been duplicated. You are now viewing the copy of the original.'),
                        );
                    });

                    it('navigates to new entry after successful duplication', async () => {
                        // Set form as clean to bypass unsaved changes dialog
                        (useFormContext as jest.Mock).mockReturnValue({
                            formState: { isDirty: false, isValid: true },
                            reset: jest.fn(),
                        });

                        (useRegisterEntryActions as jest.Mock).mockReturnValue([mockDuplicateAction]);

                        const { user } = render(
                            <RegisterEntryHeaderActions
                                onNavigateBack={mockOnNavigateBack}
                                onSave={mockOnSave}
                            />,
                        );

                        const duplicateButton = screen.getByTestId('registerEntryToolbar-actionButton-DUPLICATE');
                        await user.click(duplicateButton);

                        const dialog = screen.getByRole('dialog');
                        const confirmButton = within(dialog).getByTestId('button-confirm');
                        await user.click(confirmButton);

                        await waitFor(() => expect(mockNavigate).toHaveBeenCalledWith(`/registers/${mockRegister.tableName}/entries/new-entry-id`));
                    });

                    it('closes dialog after successful duplication', async () => {
                        // Set form as clean to bypass unsaved changes dialog
                        (useFormContext as jest.Mock).mockReturnValue({
                            formState: { isDirty: false, isValid: true },
                            reset: jest.fn(),
                        });

                        (useRegisterEntryActions as jest.Mock).mockReturnValue([mockDuplicateAction]);

                        const { user } = render(
                            <RegisterEntryHeaderActions
                                onNavigateBack={mockOnNavigateBack}
                                onSave={mockOnSave}
                            />,
                        );

                        const duplicateButton = screen.getByTestId('registerEntryToolbar-actionButton-DUPLICATE');
                        await user.click(duplicateButton);

                        const dialog = screen.getByRole('dialog');
                        const confirmButton = within(dialog).getByTestId('button-confirm');
                        await user.click(confirmButton);

                        await waitFor(() => expect(dialog).not.toBeInTheDocument());
                    });
                });

                describe('error handling', () => {
                    it('displays an error message in case of unsuccessful duplication', async () => {
                        mockDuplicateFn.mockImplementationOnce(() => {
                            return {
                                unwrap: () => Promise.reject(new Error('Duplication failed')),
                            };
                        });

                        // Set form as clean to bypass unsaved changes dialog
                        (useFormContext as jest.Mock).mockReturnValue({
                            formState: { isDirty: false, isValid: true },
                            reset: jest.fn(),
                        });

                        (useRegisterEntryActions as jest.Mock).mockReturnValue([mockDuplicateAction]);

                        const { user } = render(
                            <RegisterEntryHeaderActions
                                onNavigateBack={mockOnNavigateBack}
                                onSave={mockOnSave}
                            />,
                        );

                        const duplicateButton = screen.getByTestId('registerEntryToolbar-actionButton-DUPLICATE');
                        await user.click(duplicateButton);

                        const dialog = screen.getByRole('dialog');
                        const confirmButton = within(dialog).getByTestId('button-confirm');
                        await user.click(confirmButton);

                        await waitFor(() => expect(mockEnqueueError).toHaveBeenCalledWith('Failed to duplicate entry'));
                    });

                    it('keeps dialog open when duplication fails and does not navigate', async () => {
                        mockDuplicateFn.mockImplementationOnce(() => {
                            return {
                                unwrap: () => Promise.reject(new Error('Duplication failed')),
                            };
                        });

                        // Set form as clean to bypass unsaved changes dialog
                        (useFormContext as jest.Mock).mockReturnValue({
                            formState: { isDirty: false, isValid: true },
                            reset: jest.fn(),
                        });

                        (useRegisterEntryActions as jest.Mock).mockReturnValue([mockDuplicateAction]);

                        const { user } = render(
                            <RegisterEntryHeaderActions
                                onNavigateBack={mockOnNavigateBack}
                                onSave={mockOnSave}
                            />,
                        );

                        const duplicateButton = screen.getByTestId('registerEntryToolbar-actionButton-DUPLICATE');
                        await user.click(duplicateButton);

                        const dialog = screen.getByRole('dialog');
                        const confirmButton = within(dialog).getByTestId('button-confirm');
                        await user.click(confirmButton);

                        expect(dialog).toBeInTheDocument();
                        expect(mockNavigate).not.toHaveBeenCalled();
                    });
                });

                describe('loading state', () => {
                    it('fires duplicate request only once when clicking confirm button multiple times', async () => {
                        // Set form as clean to bypass unsaved changes dialog
                        (useFormContext as jest.Mock).mockReturnValue({
                            formState: { isDirty: false, isValid: true },
                            reset: jest.fn(),
                        });

                        (useRegisterEntryActions as jest.Mock).mockReturnValue([mockDuplicateAction]);

                        const { user } = render(
                            <RegisterEntryHeaderActions
                                onNavigateBack={mockOnNavigateBack}
                                onSave={mockOnSave}
                            />,
                        );

                        const duplicateButton = screen.getByTestId('registerEntryToolbar-actionButton-DUPLICATE');
                        await user.click(duplicateButton);

                        const dialog = screen.getByRole('dialog');
                        const confirmButton = within(dialog).getByTestId('button-confirm');

                        await user.click(confirmButton);
                        await user.click(confirmButton);
                        await user.click(confirmButton);

                        await waitFor(() => expect(mockDuplicateFn).toHaveBeenCalledTimes(1));
                    });
                });
            });
        });

        describe('share action', () => {
            it('copies link to clipboard when share button is clicked', async () => {
                (useRegisterEntryActions as jest.Mock).mockReturnValue([mockShareAction]);

                const { user } = render(
                    <RegisterEntryHeaderActions
                        onNavigateBack={mockOnNavigateBack}
                        onSave={mockOnSave}
                    />,
                );

                const shareButton = screen.getByTestId('registerEntryToolbar-actionButton-SHARE');
                await user.click(shareButton);
                expect(await navigator.clipboard.readText()).toBe('http://localhost/');
                expect(mockEnqueueInfo).toHaveBeenCalledWith('A link to this item has been copied to the clipboard.');
            });
            it('should display dialog when clipboard is not supported', async () => {
                (useRegisterEntryActions as jest.Mock).mockReturnValue([mockShareAction]);

                const { user } = render(
                    <RegisterEntryHeaderActions
                        onNavigateBack={mockOnNavigateBack}
                        onSave={mockOnSave}
                    />,
                );

                navigator.clipboard.writeText = jest.fn().mockRejectedValue('Clipboard API not supported');

                const shareButton = screen.getByTestId('registerEntryToolbar-actionButton-SHARE');
                await user.click(shareButton);
                expect(await navigator.clipboard.readText()).toBe('');
                expect(screen.getByTestId('button-confirm-share')).toBeInTheDocument();
            });
        });
    });
});
