import React, { useMemo } from 'react';
import { strings } from 'common/utils/i18n';
import PageToolbarGroup from 'common/components/PageToolbar/PageToolbarGroup/PageToolbarGroup';
import PageToolbarDropdown from 'common/components/PageToolbar/PageToolbarDropdown/PageToolbarDropdown';
import PageToolbarActionButton from 'common/components/PageToolbar/PageToolbarActionButton';
import PageToolbarDivider from 'common/components/PageToolbar/PageToolbarDivider';
import useFormContext from 'common/hooks/forms/useFormContext';
import { ColumnType, EntryActionType, ScreenSize, RegisterRest, RegisterEntryRecord } from 'register/types';
import { useRegisterEntryContext } from 'register/context/RegisterEntryContext';
import useRegisterEntryActions from 'register/hooks/useRegisterEntryActions/useRegisterEntryActions';
import { useCalculateAllFormulas } from 'register/hooks/useRegisterEntryActions/useCalculateAllFormulas';
import { ContextMenuItem } from '@protecht/ui-library/library/components/ContextMenu/types';
import MultipleFormulaErrorDialog from 'register/components/RegisterField/SimpleDateFormulaField/MultipleFormulaErrorDialog';
import { useDeleteAction } from 'register/hooks/useRegisterEntryActions/useDeleteAction';
import { useShareAction } from 'register/hooks/useShareAction';
import { useDuplicateAction } from 'register/hooks/useRegisterEntryActions/useDuplicateAction';
import { getFieldsByColumnType } from 'register/utils';
import { shouldFormBeDirtyBasedOnDefaults } from 'register/utils';

type RegisterEntryHeaderActionsProps = {
    onNavigateBack: () => void;
    onSave: () => void;
    onCancel?: () => void;
    isSaving?: boolean;
};

// Maximum number of main actions to show in the toolbar
// If there are more actions, they will be added to the dropdown
const MAX_MAIN_ACTIONS = 2;

const RegisterEntryHeaderActions: React.FC<RegisterEntryHeaderActionsProps> = ({ onNavigateBack, onSave, onCancel, isSaving }) => {
    const { register, isAnonymous, entry, isReadOnly, screenSize, isSubEntry, isCreate } = useRegisterEntryContext();
    const { formState } = useFormContext();
    const { isValid, isDirty } = formState;

    const isFormDirtyByDefaults = useMemo(() => {
        if (!register || !entry) {
return false;
}
        return shouldFormBeDirtyBasedOnDefaults(register as RegisterRest, entry as RegisterEntryRecord);
    }, [register, entry]);

    const effectiveDirty = isDirty || isFormDirtyByDefaults;

    const allActions = useRegisterEntryActions();
    const { calculateAllFormulas, formulaErrors, showErrorDialog, closeErrorDialog } = useCalculateAllFormulas();
    const { handleDeleteClick, ConfirmationDialogComponent: DeleteConfirmDialog } = useDeleteAction();
    const { handleShareEntry, ShareDialogComponent: ShareDialog } = useShareAction(entry);
    const { handleDuplicateClick, ConfirmationDialogComponent: DuplicateConfirmDialog } = useDuplicateAction();

    // Define all action handlers
    const actionHandlers = {
        [EntryActionType.DELETE]: handleDeleteClick,
        [EntryActionType.SHARE]: handleShareEntry,
        [EntryActionType.CALCULATE_FORMULAS]: () => void calculateAllFormulas(),
        [EntryActionType.DUPLICATE]: handleDuplicateClick,
    };

    const handleSave = async () => {
        if (isAnonymous) {
            const formulaCalculationSuccess = await calculateAllFormulas();
            if (!formulaCalculationSuccess) {
                // Don't proceed with save if formula calculation failed
                return;
            }
        }
        onSave();
    };

    const hasCustomIdOrHintField = useMemo(() => {
        if (!register) {
            return false;
        }
        return [...getFieldsByColumnType(register, ColumnType.CUSTOM_ID), ...getFieldsByColumnType(register, ColumnType.HINT)].length > 0;
    }, [register]);

    // If there would be only one item in the dropdown, show all as buttons instead
    // Only use dropdown when there are more than MAX_MAIN_ACTIONS + 1 items
    const shouldShowAllAsButtons = allActions.length <= MAX_MAIN_ACTIONS + 1;
    const mainActions = shouldShowAllAsButtons ? allActions : allActions.slice(0, MAX_MAIN_ACTIONS);
    const moreDropdownActions = shouldShowAllAsButtons ? [] : allActions.slice(MAX_MAIN_ACTIONS);
    const showDivider = allActions.length > 0;

    const submitButtonLabel = isAnonymous && !isSubEntry ? strings('common:button.submit') : strings('common:button.save');

    return (
        <>
            <PageToolbarGroup
                alignSelf="flex-end"
                data-testid="registerEntryToolbar-actions"
                flex="0 0 auto"
            >
                {mainActions.map((action) => {
                    const { icon: Icon, isDisabled, key, color, label } = action;
                    return (
                        <PageToolbarActionButton
                            label={label}
                            key={key}
                            disabled={isDisabled ? isDisabled(register, entry, isReadOnly, isSubEntry) : false}
                            icon={<Icon />}
                            sx={{ color }}
                            hideLabel={screenSize !== ScreenSize.XL}
                            dataTestId={`registerEntryToolbar-actionButton-${key}`}
                            onClick={() => actionHandlers[key]?.()}
                        />
                    );
                })}
                {moreDropdownActions.length > 0 && (
                    <PageToolbarDropdown
                        hideButtonLabel={screenSize === ScreenSize.SM || screenSize === ScreenSize.LG}
                        items={moreDropdownActions.map((item) => {
                            const { label, icon: Icon, isDisabled, key } = item;
                            const dropdownItem: ContextMenuItem = {
                                label: label,
                                disabled: isDisabled ? isDisabled(register, entry, isReadOnly, isSubEntry) : false,
                                icon: <Icon />,
                                action: () => actionHandlers[key]?.(),
                            };

                            return dropdownItem;
                        })}
                        dataTestId="registerEntryToolbar-moreActionsDropdown"
                    />
                )}
                {showDivider && <PageToolbarDivider />}
                {(!isAnonymous || onCancel) && (
                    <PageToolbarActionButton
                        label={strings('common:button.cancel')}
                        onClick={onNavigateBack}
                        forceMinWidth
                        dataTestId="registerEntryToolbar-cancelButton"
                    />
                )}
                <PageToolbarActionButton
                    label={submitButtonLabel}
                    variant="primary"
                    onClick={handleSave}
                    disabled={
                        (!isCreate && !entry?.editableByCurrentUser) ||
                        !isValid ||
                        isReadOnly ||
                        (
                            !isAnonymous &&
                            (
                                !effectiveDirty ||
                                ((!hasCustomIdOrHintField || isSaving) && !isDirty)
                            )
                        )
                    }
                    forceMinWidth
                    dataTestId="registerEntryToolbar-saveButton"
                />
            </PageToolbarGroup>
            {DeleteConfirmDialog}
            {ShareDialog}
            <MultipleFormulaErrorDialog
                visible={showErrorDialog}
                onClose={closeErrorDialog}
                errors={formulaErrors}
            />
            {DuplicateConfirmDialog}
        </>
    );
};
export default RegisterEntryHeaderActions;
