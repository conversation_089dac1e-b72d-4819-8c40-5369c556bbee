import React, { useCallback, useEffect, useMemo } from 'react';
import { useDispatch } from 'react-redux';
import { generatePath, useNavigate, useParams } from 'react-router';
import { FieldValues } from 'react-hook-form';
import { useConfirmationAlert } from 'context/ConfirmationAlertProvider/useConfirmationAlert';
import { useRegisterEntryContext } from 'register/context/RegisterEntryContext';
import useFormContext from 'common/hooks/forms/useFormContext';
import { AlertType, DialogType } from 'common/types';
import { removeRegisterFormState, setRegisterFormState } from 'register/reducer';
import MainFormHandler from './MainFormHandler';
import { strings } from 'common/utils/i18n';
import PageToolbar from 'common/components/PageToolbar';
import PageToolbarTitle from 'common/components/PageToolbar/PageToolbarTitle/PageToolbarTitle';
import PageToolbarGroup from 'common/components/PageToolbar/PageToolbarGroup/PageToolbarGroup';
import RegisterEntryHeaderActions from './RegisterEntryHeaderActions';
import { ScreenSize } from 'register/types';
import RegisterState from './RegisterState';
import { getEntryBreadcrumbs } from 'register/utils';
import { RegisterPath } from 'register/routes';

type Props = {
    onSave: (formValues: FieldValues) => void;
    onCancel?: () => void;
    isSaving?: boolean;
};

const RegisterEntryHeader: React.FC<Props> = ({ onSave, onCancel, isSaving }) => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { handleSubmit, formState } = useFormContext();
    const { isAnonymous, isSubEntry, register, entry, screenSize } = useRegisterEntryContext();
    const { showConfirmationAlert } = useConfirmationAlert();
    const isLargerScreen = screenSize === ScreenSize.LG || screenSize === ScreenSize.XL;
    const { appId } = useParams<{ appId: string }>();

    const { isValid, isDirty } = formState;

    useEffect(() => {
        if (register?.id) {
            dispatch(setRegisterFormState({ registerId: register.id, isDirty: formState.isDirty, isValid: formState.isValid }));

            return () => {
                dispatch(removeRegisterFormState(register.id!));
            };
        }
    }, [formState, dispatch, register?.id]);

    const onNavigateBack = useCallback(() => {
        if (onCancel) {
            if (isDirty && isSubEntry) {
                showConfirmationAlert({
                    isCancelPrimary: !isValid,
                    onBack: () => {
                        onCancel();
                    },
                    onConfirm: handleSubmit(onSave),
                    contentText: isValid ? strings('common:message.unsavedChanges') : strings('common:message.discardChanges'),
                    type: isValid ? AlertType.Warning : AlertType.Error,
                    dialogType: isValid ? DialogType.UNSAVED : DialogType.DISCARD,
                    title: isValid ? strings('common:title.saveChanges') : strings('common:title.discardChanges'),
                    confirmButtonLabel: isValid ? strings('common:button.save') : strings('common:button.discard'),
                    cancelButtonLabel: strings('common:button.cancel'),
                });
            } else {
                onCancel();
            }
        } else {
            if (register) {
                void navigate(
                    generatePath(RegisterPath.REGISTER_LIST_REGISTER, {
                        appId: appId || register?.applicationId!.toString(),
                        tableName: register.tableName!,
                    }),
                );
            } else {
                void navigate(-1);
            }
        }
    }, [onCancel, isDirty, isSubEntry, showConfirmationAlert, isValid, handleSubmit, onSave, register, navigate, appId]);

    const breadcrumbs = useMemo(() => {
        if (!register || isAnonymous) {
            return [];
        }

        return getEntryBreadcrumbs(register);
    }, [register, isAnonymous]);

    return (
        <>
            <MainFormHandler />
            <PageToolbar
                onBack={!isAnonymous || !!onCancel ? onNavigateBack : undefined}
                breadcrumbs={isLargerScreen ? breadcrumbs : []}
                data-testid="registerEntryToolbar"
            >
                <PageToolbarGroup
                    flex="1"
                    justifyContent={isLargerScreen ? 'space-between' : 'flex-start'}
                    alignItems={isLargerScreen ? 'center' : 'flex-start'}
                    flexDirection={isLargerScreen ? 'row' : 'column'}
                    gap={isLargerScreen ? '20px' : '10px'}
                >
                    <PageToolbarGroup maxWidth="100%">
                        <PageToolbarTitle data-testid="registerEntryToolbar-title">
                            {isAnonymous ? register?.label : strings('ermMessages:register_entry_idlabel', { 0: entry?.record?.id })}
                        </PageToolbarTitle>
                        {!isAnonymous && <RegisterState />}
                    </PageToolbarGroup>
                    <RegisterEntryHeaderActions
                        onNavigateBack={onNavigateBack}
                        onSave={handleSubmit(onSave)}
                        onCancel={onCancel}
                        isSaving={isSaving}
                    />
                </PageToolbarGroup>
            </PageToolbar>
        </>
    );
};

export default RegisterEntryHeader;
