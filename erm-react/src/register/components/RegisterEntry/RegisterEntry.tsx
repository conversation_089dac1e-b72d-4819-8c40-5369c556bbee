import React, { useCallback, useEffect, useMemo } from 'react';
import { RegisterEntryRest, RegisterStateDefinition, ScreenSize } from 'register/types';
import Loading from 'common/components/Loading';
import useMediaQuery from '@mui/material/useMediaQuery';
import { skipToken } from '@reduxjs/toolkit/query';
import { useRdrsvGetEntryByIdUsingGetQuery, useRdrsvCreateNewEntryUsingPostMutation, useTmrsGetRegisterConfigUsingGetQuery } from 'register/rtkApi';
import { useRegisterStateTransition } from 'register/hooks/useRegisterStateTransition';
import { strings } from 'common/utils/i18n';
import { RegisterEntryProvider } from 'register/context/RegisterEntryContext';
import { IdWithNameRest } from 'api/generated/types';
import Box from '@mui/material/Box';
import RegisterEntryFormWrapper from './RegisterEntryFormWrapper/RegisterEntryFormWrapper';
import { AlertType } from '@protecht/ui-library/library/types';
import InfoPage from 'common/components/InfoPage';
import { getErrorCode } from 'api/utils';
import Button from '@protecht/ui-library/library/components/Button';
import { InfoBoxActions, InfoBoxContent } from '@protecht/ui-library/library/components/InfoBox';
import { useNavigate } from 'react-router';
import { isProduction } from 'config';

type Props = {
    tableName?: string;
    entryId?: string;
    isAnonymous?: boolean;
    isSubEntry?: boolean;
    isCreate?: boolean;
    isReadOnly?: boolean;
    onCancel?: () => void;
    onSave?: (data: IdWithNameRest) => void;
};

const MemoizedRegisterEntryFormWrapper = React.memo(RegisterEntryFormWrapper);

const RegisterEntry: React.FC<Props> = ({
    isAnonymous = false,
    isCreate = false,
    isReadOnly = false,
    isSubEntry = false,
    tableName,
    entryId,
    onCancel,
    onSave,
}) => {
    const portalContainerRef = React.useRef<HTMLDivElement>(null);
    const [createNewRegisterEntry, newRegisterEntryResult] = useRdrsvCreateNewEntryUsingPostMutation();

    const registerEntryId = entryId ? parseInt(entryId) : newRegisterEntryResult?.data?.record?.id;

    const { data: register, error: registerError } = useTmrsGetRegisterConfigUsingGetQuery(tableName ? { tableName } : skipToken);

    const navigate = useNavigate();

    const {
        data: entry,
        isLoading: isRegisterEntryLoading,
        error: registerEntryError,
    } = useRdrsvGetEntryByIdUsingGetQuery(tableName && registerEntryId ? { tableName, entryId: registerEntryId } : skipToken);

    // TODO: implement other scenarios
    const isCreateForm = useMemo(() => {
        if (isSubEntry) {
            return isCreate && !entry?.completed;
        }
        return (isAnonymous || isCreate) && !entry?.completed;
    }, [isSubEntry, isAnonymous, entry?.completed, isCreate]);

    useEffect(() => {
        if (isCreateForm && register) {
            const registerData = {
                record: {
                    label: register.label,
                    applicationName: register.applicationName,
                },
            };
            void createNewRegisterEntry({ initial: true, registerDataRest: registerData });
        }
    }, [createNewRegisterEntry, isCreateForm, register]);

    // TODO: implement better way to handle screen size
    // workaround to use custom breakpoints for register entry instead of breakpoints defined in theme because they are different
    const isSmallScreen = useMediaQuery('(max-width:453px)');
    const isMediumScreen = useMediaQuery('(max-width:833px)');
    const isLargeScreen = useMediaQuery('(max-width:1440px)');
    const screenSize = useMemo(() => {
        if (isSmallScreen) {
            return ScreenSize.SM;
        } else if (isMediumScreen) {
            return ScreenSize.MD;
        } else if (isLargeScreen) {
            return ScreenSize.LG;
        } else {
            return ScreenSize.XL;
        }
    }, [isSmallScreen, isMediumScreen, isLargeScreen]);

    const registerEntryForStateTransition = useMemo(() => {
        if (!entry?.record?.id) {
return null;
}
        return {
            id: entry.record.id,
            status: entry.record.status,
            sections: entry.record?.sections || [],
        } as RegisterEntryRest;
    }, [entry]);

    const registerForStateTransition: {
        stateful: boolean;
        stateDefinition?: RegisterStateDefinition;
    } = useMemo(() => {
        if (!register?.stateful || !register?.stateDefinition) {
            return { stateful: false };
        }

        const states = (register.stateDefinition.states || [])
            .filter((state) => state.id !== undefined && state.name !== undefined)
            .map((state) => ({
                id: state.id as number,
                name: state.name as string,
                isInitial: state.isInitial ?? false,
                isFinal: state.isFinal ?? false,
            }));

        const stateTransitions = (register.stateDefinition.stateTransitions || [])
            .filter(
                (t) =>
                    t.id !== undefined &&
                    t.name !== undefined &&
                    t.currentState !== undefined &&
                    t.newState !== undefined &&
                    t.transitionOrder !== undefined,
            )
            .map((t) => ({
                id: t.id as number,
                name: t.name as string,
                currentState: t.currentState as string,
                newState: t.newState as string,
                transitionOrder: t.transitionOrder as number,
                enabled: true,
                visible: true,
            }));

        return {
            stateful: register.stateful,
            stateDefinition: {
                isDefault: register.stateDefinition.isDefault ?? false,
                states,
                stateTransitions,
            },
        };
    }, [register]);

    const { isFinalState } = useRegisterStateTransition({
        entry: registerEntryForStateTransition || ({} as RegisterEntryRest),
        register: registerForStateTransition,
        registerId: register?.id,
    });

    const isReadOnlyFinalState = registerEntryForStateTransition && registerForStateTransition ? isFinalState : false;

    const effectiveIsReadOnly = isReadOnly || isReadOnlyFinalState;

    const containerRef = React.useRef<HTMLDivElement>(null);

    const onErrorBack = useCallback(() => {
        if (onCancel) {
            onCancel();
        } else {
            void navigate(-1);
        }
    }, [onCancel, navigate]);

    if (getErrorCode(registerError) === 404) {
        // show error when register api failed
        return (
            <InfoPage
                type={AlertType.Warning}
                subtitle={strings('register:error.formNotFound')}
                message={isAnonymous ? strings('register:error.formAnonymousNotFoundMessage') : strings('register:error.formNotFoundMessage')}
            />
        );
    } else if (getErrorCode(registerEntryError) === 403 || getErrorCode(newRegisterEntryResult.error) === 403) {
        // show error when user does not have permission to view the entry form
        return (
            <InfoPage
                type={AlertType.Warning}
                title={register?.label || ''}
                subtitle={strings('register:message.formNoPermission')}
                message={isAnonymous ? strings('register:message.formAnonymousNoPermissionMessage') : strings('register:message.formNoPermissionMessage')}
                onBack={isAnonymous && !onCancel ? undefined : onErrorBack}
            />
        );
    } else if (registerError || registerEntryError || newRegisterEntryResult.error) {
        // show generic error
        const errorCode = getErrorCode(registerError || registerEntryError || newRegisterEntryResult.error);
        return (
            <InfoPage
                type={AlertType.Error}
                title={register?.label || undefined}
                subtitle={strings('common:message.unexpectedError')}
                message={strings('common:message.unexpectedErrorMessage')}
                onBack={isAnonymous && !onCancel ? undefined : onErrorBack}
            >
                <InfoBoxContent>{strings('common:message.errorCode', { code: errorCode })}</InfoBoxContent>
                {!isAnonymous && (
                    <InfoBoxActions>
                        <Button
                            size="large"
                            variant="outlined"
                            onClick={() => {
                                if (isProduction) {
                                    window.location.href = ProtechtDictionary.siteUrl;
                                } else {
                                    void navigate('/');
                                }
                            }}
                        >
                            {strings('common:button.returnHome')}
                        </Button>
                    </InfoBoxActions>
                )}
            </InfoPage>
        );
    }

    if (isRegisterEntryLoading || newRegisterEntryResult.isLoading || !register || (!isCreateForm && !isAnonymous && !entry)) {
        return <Loading />;
    } else {
        return (
            <>
                {/* portal container for creating new entry within the actual form */}
                <Box
                    ref={portalContainerRef}
                    id="portal-container"
                ></Box>
                <RegisterEntryProvider
                    isAnonymous={isAnonymous}
                    isCreate={isCreateForm}
                    isReadOnly={effectiveIsReadOnly}
                    isSubEntry={isSubEntry}
                    register={register}
                    entry={entry}
                    portalContainer={portalContainerRef}
                    containerRef={containerRef}
                    screenSize={screenSize}
                >
                    <MemoizedRegisterEntryFormWrapper
                        onCancel={onCancel}
                        onSave={onSave}
                        containerRef={containerRef}
                    />
                </RegisterEntryProvider>
            </>
        );
    }
};

export default RegisterEntry;
