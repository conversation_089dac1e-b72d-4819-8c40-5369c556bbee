import React, { useCallback, useEffect, useState } from 'react';
import { ColumnType, RegisterEntryRest, RegisterRest, ScreenSize, SectionMetaData } from 'register/types';
import { useNavigate } from 'react-router';
import Grid from '@mui/material/Grid';
import RegisterEntryHeader from '../RegisterEntryHeader';
import {
    formatDateFieldValue,
    getFieldFromRegisterByColumnName,
    getRegisterEntryDisplayFieldValue,
    getRegisterEntryField,
    getRegisterTabs,
    isAttachmentField,
    isGalleryField,
    isRelatedLinksField,
} from 'register/utils';
import { FieldValues } from 'react-hook-form';
import useSnackbar from 'common/hooks/useSnackbar';
import { strings } from 'common/utils/i18n';
import { useRdrsvUpdateEntryUsingPutMutation, useRdrsvUpdateRelatedLinkedEntriesUsingPutMutation } from 'register/rtkApi';
import { Attachment } from '@protecht/ui-library/library/components/FileDropzone';
import { useArsDeleteAttachmentUsingDeleteMutation, useLinkTempAttachmentsMutation } from 'common/api/attachments';
import { useDispatch } from 'react-redux';
import RegisterTabs from '../RegisterTabs/RegisterTabs';
import { Dictionary } from 'lodash';
import { useRegisterEntryContext } from 'register/context/RegisterEntryContext';
import { IdWithNameRest, LinkData } from 'api/generated/types';
import { IdWithNameAndStatusRest } from 'app/types';
import { EMPTY_OPTION_VALUE, EMPTY_VALUE, NO_SELECTED_VALUE, REGISTER_DATE_FORMAT } from '@protecht/ui-library/library/constants';
import {
    MAIN_TAB_NAME,
    OUTER_SPACING_HORIZONTAL_MD,
    OUTER_SPACING_HORIZONTAL_SM,
    OUTER_SPACING_VERTICAL_MD,
    OUTER_SPACING_VERTICAL_SM,
} from 'register/constants';
import { PROTECHT_DATE_TIME_FORMAT } from 'common/constants';
import RegisterSections from '../RegisterContents/RegisterSections';
import RegisterTabsContent from '../RegisterContents/RegisterTabsContent';
import { TagTable } from 'library/types';

type Props = {
    onCancel?: () => void;
    onSave?: (data: IdWithNameRest) => void;
    containerRef: React.RefObject<HTMLDivElement>;
};

const RegisterEntryWrapper: React.FC<Props> = ({ onCancel, onSave, containerRef }) => {
    const { register, isAnonymous, entry, screenSize } = useRegisterEntryContext();

    const dispatch = useDispatch();
    const navigate = useNavigate();

    const [updateRegisterEntry, { isLoading: isSavingRegisterEntry }] = useRdrsvUpdateEntryUsingPutMutation();

    const [linkTempAttachment] = useLinkTempAttachmentsMutation();
    const [deleteAttachment] = useArsDeleteAttachmentUsingDeleteMutation();

    const [updateRelatedLinks] = useRdrsvUpdateRelatedLinkedEntriesUsingPutMutation();

    const { enqueueSuccess, enqueueError } = useSnackbar();

    const [tabs, setTabs] = useState<Dictionary<SectionMetaData[]>>();
    useEffect(() => {
        if (register) {
            const registerTabs = getRegisterTabs(register as RegisterRest, isAnonymous);
            setTabs(registerTabs);
        }
    }, [dispatch, register, isAnonymous]);

    const [selectedTab, setSelectedTab] = useState<number>(0);
    const handleTabChange = useCallback((_event: React.SyntheticEvent, newValue: number) => {
        setSelectedTab(newValue);
    }, []);

    const onEntrySave = useCallback(
        async (formValues: FieldValues) => {
            const attachmentFields = {};
            if (register && entry && entry.record && entry.record.id) {
                for (const fieldName in formValues) {
                    if (isAttachmentField(fieldName, register as RegisterRest) || isGalleryField(fieldName, register as RegisterRest)) {
                        const formFieldValue = (formValues[fieldName] as Attachment[]) || [];
                        const entryField = getRegisterEntryField(entry.record as RegisterEntryRest, fieldName);
                        const previousAttachmentsUuids: string[] = entryField?.['parameters']?.['uuids'] ?? [];
                        const addAttachments =
                            formFieldValue.filter((attachment: Attachment) => !previousAttachmentsUuids.some((a) => a === attachment.uuid)) ?? [];
                        const removeAttachments = previousAttachmentsUuids.filter((attachment) => !formFieldValue.some((a) => a.uuid === attachment)) ?? [];
                        const newParameters = { ...entryField!['parameters'] };
                        const linkAttachments = async () => {
                            for (const attachment of addAttachments) {
                                await linkTempAttachment({
                                    fileIdentifier: attachment.uuid!,
                                    fileName: attachment.name,
                                    linkedColumn: entryField!.fieldName!,
                                    linkedId: entry.record!.id!,
                                    linkedTable: register.tableName!,
                                })
                                    .unwrap()
                                    .then((result) => {
                                        newParameters['fileNames'] = [...(newParameters['fileNames'] ?? []), result.fileName];
                                        newParameters['fileSizesInBytes'] = [...(newParameters['fileSizesInBytes'] ?? []), result.fileSize];
                                        newParameters['uuids'] = [...(newParameters['uuids'] ?? []), result.uuid];
                                    })
                                    .catch(() => {
                                        enqueueError(strings('register:error.linkAttachment', { name: attachment.name }));
                                    });
                            }
                        };
                        const deleteAttachments = async () => {
                            for (const attachmentUuid of removeAttachments) {
                                const index = newParameters['uuids'].findIndex((uuid: string) => uuid === attachmentUuid);
                                const attachmentName: string = newParameters['fileNames'][index];
                                await deleteAttachment({
                                    attachmentUuid,
                                })
                                    .unwrap()
                                    .then(() => {
                                        const uuids = [...newParameters['uuids']].splice(index, 1);
                                        newParameters['uuids'] = uuids;
                                        const fileNames = [...newParameters['fileNames']];
                                        newParameters['fileNames'] = fileNames.splice(index, 1);
                                        const fileSizesInBytes = [...newParameters['fileSizesInBytes']];
                                        newParameters['fileSizesInBytes'] = fileSizesInBytes.splice(index, 1);
                                    })
                                    .catch(() => {
                                        enqueueError(strings('register:error.deleteAttachment', { name: attachmentName }));
                                    });
                            }
                        };
                        await linkAttachments();
                        await deleteAttachments();
                        attachmentFields[fieldName] = newParameters;
                    }
                    if (isRelatedLinksField(fieldName, register as RegisterRest)) {
                        const formFieldValue = formValues[fieldName] || [];

                        if (!(formFieldValue.length === 1 && formFieldValue[0] === EMPTY_VALUE)) {
                            await updateRelatedLinks({
                                tableName: register.tableName!,
                                entryId: entry.record.id!,
                                body: formFieldValue.map((item: LinkData) => ({
                                    parentId: item.params?.relatedEntryReverse === 'true' ? entry.record!.id : item.targetId,
                                    childId: item.params?.relatedEntryReverse === 'true' ? item.targetId : entry.record!.id,
                                    relationId: item.params?.relatedEntryTypeId,
                                })),
                            })
                                .unwrap()
                                .catch(() => {
                                    enqueueError(strings('register:error.linkRelatedEntries'));
                                });
                        }
                    }
                }
                const body = {
                    ...entry,
                    record: {
                        ...entry.record,
                        sections: entry.record?.sections?.map((section) => ({
                            ...section,
                            fields: section.fields?.map((field) => {
                                const formValue = field.fieldName ? formValues[field.fieldName] : undefined;
                                const registerField = getFieldFromRegisterByColumnName(register as RegisterRest, field.fieldName!);
                                if (registerField?.columnType === ColumnType.ATTACHMENT || registerField?.columnType === ColumnType.IMAGES) {
                                    const simpleValue = formValue?.length;
                                    return {
                                        ...field,
                                        parameters: attachmentFields[field.fieldName!],
                                        simpleValue: simpleValue ? [simpleValue] : undefined,
                                    };
                                }
                                if (
                                    registerField?.columnType === ColumnType.DUE_DATE ||
                                    registerField?.columnType === ColumnType.DATE ||
                                    registerField?.columnType === ColumnType.TIMESTAMP ||
                                    registerField?.columnType === ColumnType.TIMESTAMP_WITH_TIMEZONE
                                ) {
                                    const dateFormat =
                                        registerField?.columnType === ColumnType.DUE_DATE || registerField?.columnType === ColumnType.DATE
                                            ? REGISTER_DATE_FORMAT
                                            : PROTECHT_DATE_TIME_FORMAT;

                                    return {
                                        ...field,
                                        simpleValue: formValue ? [formatDateFieldValue(formValue, dateFormat)] : undefined,
                                    };
                                }
                                if (registerField?.columnType === ColumnType.MULTISELECT_LIST) {
                                    return {
                                        ...field,
                                        simpleValue: formValue.length > 0 ? formValue : undefined,
                                    };
                                }

                                if (registerField?.columnType === ColumnType.LIST) {
                                    return {
                                        ...field,
                                        simpleValue:
                                            formValue === EMPTY_OPTION_VALUE ? [EMPTY_VALUE] : formValue === NO_SELECTED_VALUE ? undefined : [formValue],
                                    };
                                }

                                if (
                                    registerField?.columnType === ColumnType.TABLE ||
                                    registerField?.columnType === ColumnType.ACTIONS ||
                                    registerField?.columnType === ColumnType.BUSINESS_UNIT ||
                                    registerField?.columnType === ColumnType.USER ||
                                    registerField?.columnType === ColumnType.MULTISELECT_LIBRARY ||
                                    registerField?.columnType === ColumnType.STATE ||
                                    registerField?.columnType === ColumnType.COUNTRY ||
                                    registerField?.columnType === ColumnType.ROLE ||
                                    registerField?.columnType === ColumnType.CENTRAL_LIBRARY ||
                                    registerField?.columnType === ColumnType.CONTROL ||
                                    registerField?.columnType === ColumnType.RISK_CAUSE ||
                                    registerField?.columnType === ColumnType.RISK_EVENT
                                ) {
                                    return {
                                        ...field,
                                        simpleValue: formValue.length > 0 ? formValue.map((item: IdWithNameAndStatusRest) => item.id.toString()) : undefined,
                                    };
                                }

                                if (registerField?.columnType === ColumnType.SIGN_OFF) {
                                    return {
                                        ...field,
                                        parameters: {
                                            ...field.parameters,
                                            userName: formValue ? [formValue.name] : undefined,
                                        },
                                        simpleValue: formValue ? [formValue.id] : undefined,
                                    };
                                }

                                if (registerField?.columnType === ColumnType.CURRENCY) {
                                    return {
                                        ...field,
                                        simpleValue: formValue ? [parseFloat(formValue)] : undefined,
                                    };
                                }

                                if (registerField?.columnType === ColumnType.HYPERLINK) {
                                    return {
                                        ...field,
                                        simpleValue: formValue ? [JSON.stringify(formValue)] : undefined,
                                    };
                                }
                                if (registerField?.columnType === ColumnType.RICH_TEXT) {
                                    return {
                                        ...field,
                                        simpleValue: formValue ? [formValue] : undefined,
                                    };
                                }
                                if (registerField?.columnType === ColumnType.GPS_POSITION) {
                                    return {
                                        ...field,
                                        simpleValue: formValue ? [`${formValue?.lat},${formValue?.lng}`] : undefined,
                                    };
                                }

                                if (registerField?.columnType === ColumnType.SLIDER) {
                                    return {
                                        ...field,
                                        simpleValue: formValue ? [formValue.toString()] : undefined,
                                    };
                                }

                                if (isRelatedLinksField(field.fieldName!, register as RegisterRest)) {
                                    return {
                                        ...field,
                                        simpleValue: undefined,
                                    };
                                }

                                if (registerField?.columnType === ColumnType.TAGS) {
                                    return {
                                        ...field,
                                        simpleValue: formValue.length > 0 ? formValue.map((item: TagTable) => `${item.type.name}:${item.value}`) : undefined,
                                    };
                                }

                                if (registerField?.columnType === ColumnType.DATETIME_FORMULA) {
                                    return {
                                        ...field,
                                        simpleValue: formValue ? [formValue.toString()] : undefined,
                                    };
                                }

                                if (registerField?.columnType === ColumnType.SIMPLE_FORMULA) {
                                    return {
                                        ...field,
                                        simpleValue: formValue ? [formValue.toString()] : undefined,
                                    };
                                }

                                return {
                                    ...field,
                                    simpleValue: typeof formValue !== 'undefined' && formValue !== '' ? [formValue] : undefined,
                                };
                            }),
                        })),
                    },
                };
                await updateRegisterEntry({ registerDataRest: body, regId: register.id!, entryId: entry.record.id, evalFormulas: true }).then((result) => {
                    if ('error' in result) {
                        enqueueError(strings('register:error.registerEntrySaved'));
                    } else {
                        if (isAnonymous && !onSave) {
                            void navigate('confirmation', { state: { registerTitle: register.label, entry } });
                        } else {
                            const name = getRegisterEntryDisplayFieldValue(register, body);
                            enqueueSuccess(strings('register:message.registerEntrySaved'));
                            onSave?.({ id: entry.record!.id, name });
                        }
                    }
                });
            }
        },
        [
            register,
            entry,
            updateRegisterEntry,
            linkTempAttachment,
            enqueueError,
            deleteAttachment,
            updateRelatedLinks,
            isAnonymous,
            onSave,
            navigate,
            enqueueSuccess,
        ],
    );

    return (
        <Grid
            container
            direction="column"
            height="100%"
        >
            <Grid
                item
                container
                direction="column"
                borderBottom="1px solid"
                borderColor="protechtGrey.grey_238"
                id="register-entry-header"
            >
                <Grid
                    item
                    sx={{ maxWidth: '100% !important' }}
                >
                    <RegisterEntryHeader
                        onSave={onEntrySave}
                        onCancel={onCancel}
                        isSaving={isSavingRegisterEntry}
                    />
                </Grid>
                {register?.tabLayout && tabs && (
                    <Grid
                        item
                        sx={{ maxWidth: '100% !important' }}
                    >
                        <RegisterTabs
                            selectedTab={selectedTab}
                            tabs={tabs}
                            onSelectedTabChanged={handleTabChange}
                        />
                    </Grid>
                )}
            </Grid>
            <Grid
                item
                bgcolor="protechtGrey.grey_250"
                flex={1}
                padding={
                    screenSize === ScreenSize.SM
                        ? `0 ${OUTER_SPACING_HORIZONTAL_SM}px ${OUTER_SPACING_VERTICAL_SM}px`
                        : `0 ${OUTER_SPACING_HORIZONTAL_MD}px ${OUTER_SPACING_VERTICAL_MD}px`
                }
                overflow="auto"
                width="100%"
                height="100%"
                ref={containerRef}
            >
                {register && !register?.tabLayout && (
                    <RegisterSections
                        tabName={MAIN_TAB_NAME}
                        sections={register.sections as SectionMetaData[]}
                    />
                )}
                {register && register?.tabLayout && tabs && (
                    <RegisterTabsContent
                        selectedTab={selectedTab}
                        tabs={tabs}
                    />
                )}
            </Grid>
        </Grid>
    );
};

export default RegisterEntryWrapper;
