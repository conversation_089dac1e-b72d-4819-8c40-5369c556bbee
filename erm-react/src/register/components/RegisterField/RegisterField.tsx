import React, { useMemo } from 'react';
import { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import { SectionFieldMetaData } from '@protecht/ui-library/library/types/types';
import BooleanRegisterField from './BooleanRegisterField';
import MultiLineTextRegisterField from './MultiLineTextRegisterField';
import NumericRegisterField from './NumericRegisterField';
import SingleLineTextRegisterField from './SingleLineTextRegisterField';
import SpacerRegisterField from './SpacerRegisterField';
import UnsupportedRegisterField from './UnsupportedRegisterField';
import { ListRegisterField, MultiselectListRegisterField } from './ListRegisterField';
import DueDateRegisterField from './DueDateRegisterField';
import StaticTextRegisterField from './StaticTextRegisterField';
import DateTimeRegisterField from './DateTimeRegisterField';
import DateReg<PERSON><PERSON>ield from './DateRegisterField';
import CurrencyReg<PERSON><PERSON>ield from './CurrencyRegisterField';
import IntegerRegisterField from './IntegerRegisterField';
import DateTimeTimezoneRegisterField from './DateTimeTimezoneRegisterField';
import HintRegisterField from './HintRegisterField';
import TableRegisterField from './TableRegisterField';
import { ColumnType, SectionMetaData } from 'register/types';
import { useSelector } from 'store';
import { selectEditableFieldRule } from 'rules/selectors';
import { getFieldLabel } from 'register/utils';
import AttachmentRegisterField from './AttachmentRegisterField';
import { selectRegisterStructure } from 'register/selectors';
import { useRegisterEntryContext } from 'register/context/RegisterEntryContext';
import BusinessUnitRegisterField from './BusinessUnitRegisterField';
import { UserRegisterField } from './UserRegisterField';
import { ConditionalStyling } from 'rules/types';
import ImageGalleryRegisterField from './ImageGalleryRegisterField';
import CountryRegisterField from './CountryRegisterField';
import StateRegisterField from './StateRegisterField';
import useFieldConditionalRules from 'rules/hooks/useFieldConditionalRules';
import SignOffRegisterField from './SignOffRegisterField';
import HyperlinkField from './HyperlinkRegisterField';
import WorkLogRegisterField from './WorkLogRegisterField';
import GPSPositionRegisterField from './GPSPositionRegisterField';
import LinkedToRegisterField from './LinkedToRegisterField/LinkedToRegisterField';
import { getConstraint, getSelectorType } from './utils';
import SliderRegisterField from './SliderRegisterField';
import { RelatedLinksRegisterField } from './RelatedLinksRegisterField';
import RTEditorRegisterField from './RTEditorRegisterField';
import TagsRegisterField from './TagsRegisterField';
import { ArrowState } from '@protecht/ui-library/library/components/SVGIcons';
import { RiskMatrixRegisterField } from './RiskMatrixRegisterField';
import ConsequenceRegisterField from './ConsequenceRegisterField';
import LikelihoodRegisterField from './LikelihoodRegisterField';
import { SelectorType } from 'common/types';
import RiskCauseRegisterField from './MultiSelectLibraryFields/RiskCauseRegisterField';
import RiskEventRegisterField from './MultiSelectLibraryFields/RiskEventRegisterField';
import RiskControlRegisterField from './MultiSelectLibraryFields/RiskControlRegisterField';
import KRIRegisterField from './MultiSelectLibraryFields/KRIRegisterField';
import DateFormulaField from './SimpleDateFormulaField/DateFormulaField';
import SimpleFormulaField from './SimpleDateFormulaField/SimpleFormulaField';
import AuditQuestionRegisterField from './MultiSelectLibraryFields/AuditQuestionRegisterField';
import BowtieRegisterField from './MultiSelectLibraryFields/BowtieRegisterField';
import RoleRegisterField from './RoleRegisterField';
import ComplexFormulaField from './ComplexFormulaField/ComplexFormulaField';
import ComplianceQuestionRegisterField from './MultiSelectLibraryFields/ComplianceQuestionRegisterField';
import MSLUserRegisterField from './MultiSelectLibraryFields/UserRegisterField';
import MSLBusinessUnitRegisterField from './MultiSelectLibraryFields/BusinessUnitRegisterField';
import CentralLibraryRegisterField from './CentralLibraryRegisterField/CentralLibraryRegisterField';
import CustomIdRegisterField from './CustomIdRegisterField';
import { SingleSelectRiskCauseRegisterField } from './RiskCauseRegisterField';
import { SingleSelectRiskEventRegisterField } from './RiskEventRegisterField';
import { ControlRegisterField } from './ControlRegisterField';

export type RegisterFieldProps = {
    field: SectionFieldMetaData;
    section: SectionMetaData;
    sectionHeaderRef?: React.RefObject<HTMLDivElement>;
};

const RegisterField: React.FC<RegisterFieldProps> = ({ field, section, sectionHeaderRef }) => {
    const { register, isReadOnly: isEntryReadOnly } = useRegisterEntryContext();
    const registerStructure = useSelector((state) => selectRegisterStructure(state, register?.id));
    const fieldRules = useFieldConditionalRules(field.columnName);
    const isFieldVisible = fieldRules?.visible !== false && !field.archived;
    const isFieldEditable = useSelector((state) => selectEditableFieldRule(state, register?.id, `${section.id}-fields`, field.columnName, registerStructure));

    const fieldProps: FormFieldBaseProps = useMemo(
        () => ({
            name: field.columnName,
            label: !field.hideLabel ? getFieldLabel(field, section) : undefined,
            emptyLabelSpacing: true,
            readOnly: isEntryReadOnly || isFieldEditable === false,
            tooltip: field.description,
        }),
        [field, section, isFieldEditable, isEntryReadOnly],
    );

    const renderField = useMemo(() => {
        return renderRegisterField(field, fieldProps, fieldRules?.style, sectionHeaderRef);
    }, [field, fieldProps, fieldRules?.style, sectionHeaderRef]);

    return isFieldVisible ? renderField : null;
};

export const renderRegisterField = (
    field: SectionFieldMetaData,
    fieldProps: FormFieldBaseProps,
    conditionalStyles?: ConditionalStyling,
    sectionHeaderRef?: React.RefObject<HTMLDivElement>,
) => {
    switch (field.columnType) {
        case ColumnType.BOOLEAN:
            return <BooleanRegisterField {...fieldProps} />;
        case ColumnType.SINGLELINE_TEXT:
            return (
                <SingleLineTextRegisterField
                    {...fieldProps}
                    field={field}
                    conditionalStyling={conditionalStyles}
                />
            );
        case ColumnType.STATIC_TEXT:
            return (
                <StaticTextRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        case ColumnType.MULTILINE_TEXT:
            return (
                <MultiLineTextRegisterField
                    {...fieldProps}
                    field={field}
                    conditionalStyling={conditionalStyles}
                />
            );
        case ColumnType.EMAIL:
            return (
                <SingleLineTextRegisterField
                    {...fieldProps}
                    field={field}
                    inputMode="email"
                />
            );
        case ColumnType.INTEGER:
            return (
                <IntegerRegisterField
                    {...fieldProps}
                    field={field}
                    conditionalStyling={conditionalStyles}
                />
            );
        case ColumnType.NUMERIC:
            return (
                <NumericRegisterField
                    {...fieldProps}
                    field={field}
                    conditionalStyling={conditionalStyles}
                />
            );
        case ColumnType.SPACER:
            return <SpacerRegisterField field={field} />;
        case ColumnType.SLIDER:
            return (
                <SliderRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        case ColumnType.DATE:
            return (
                <DateRegisterField
                    {...fieldProps}
                    field={field}
                    conditionalStyling={conditionalStyles}
                />
            );
        case ColumnType.TIMESTAMP:
            return (
                <DateTimeRegisterField
                    {...fieldProps}
                    field={field}
                    conditionalStyling={conditionalStyles}
                />
            );
        case ColumnType.TIMESTAMP_WITH_TIMEZONE:
            return (
                <DateTimeTimezoneRegisterField
                    {...fieldProps}
                    field={field}
                    conditionalStyling={conditionalStyles}
                />
            );
        case ColumnType.DUE_DATE:
            return (
                <DueDateRegisterField
                    {...fieldProps}
                    field={field}
                    conditionalStyling={conditionalStyles}
                />
            );
        case ColumnType.COUNTRY:
            return (
                <CountryRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        case ColumnType.STATE:
            return (
                <StateRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        // case ColumnType.QUESTION:
        case ColumnType.BUSINESS_UNIT:
            return (
                <BusinessUnitRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        case ColumnType.ROLE:
            return (
                <RoleRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        case ColumnType.USER:
            return (
                <UserRegisterField
                    {...fieldProps}
                    field={field}
                ></UserRegisterField>
            );
        // case ColumnType.CONTROL:
        // case ColumnType.RISK_CAUSE:
        // case ColumnType.RISK_EVENT:
        //     // case ColumnType.USER_INFO:
        //     return (
        //         <SingleSelectPickerRegisterField
        //             {...fieldProps}
        //             field={field}
        //         />
        //     );
        case ColumnType.TAGS:
            return (
                <TagsRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        case ColumnType.LIST:
            return (
                <ListRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        case ColumnType.MULTISELECT_LIST:
            return (
                <MultiselectListRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        case ColumnType.SIGN_OFF:
            return (
                <SignOffRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        case ColumnType.MULTISELECT_LIBRARY: {
            const selectorType = getSelectorType(field?.constraintProperties);

            switch (selectorType) {
                case SelectorType.RISK_CAUSE:
                    return (
                        <RiskCauseRegisterField
                            {...fieldProps}
                            field={field}
                        />
                    );
                case SelectorType.RISK_EVENT:
                    return (
                        <RiskEventRegisterField
                            {...fieldProps}
                            field={field}
                        />
                    );
                case SelectorType.CONTROL:
                    return (
                        <RiskControlRegisterField
                            {...fieldProps}
                            field={field}
                        />
                    );
                case SelectorType.KRI:
                    return (
                        <KRIRegisterField
                            {...fieldProps}
                            field={field}
                        />
                    );
                case SelectorType.AUDIT_QUESTION:
                    return (
                        <AuditQuestionRegisterField
                            {...fieldProps}
                            field={field}
                        />
                    );
                case SelectorType.BOWTIE:
                    return (
                        <BowtieRegisterField
                            {...fieldProps}
                            field={field}
                        />
                    );
                case SelectorType.QUESTION:
                    return (
                        <ComplianceQuestionRegisterField
                            {...fieldProps}
                            field={field}
                        />
                    );
                case SelectorType.USER:
                    return (
                        <MSLUserRegisterField
                            {...fieldProps}
                            field={field}
                        />
                    );
                case SelectorType.BUSINESS_UNIT:
                    return (
                        <MSLBusinessUnitRegisterField
                            {...fieldProps}
                            field={field}
                        />
                    );
                default:
                    return <UnsupportedRegisterField {...fieldProps} />;
            }
        }
        case ColumnType.SCALE_LIKELIHOOD:
            return (
                <LikelihoodRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        case ColumnType.SCALE_CONSEQUENCE:
            return (
                <ConsequenceRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        case ColumnType.CURRENCY:
            return (
                <CurrencyRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        case ColumnType.TABLE: {
            return (
                <TableRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        }
        case ColumnType.ACTIONS: {
            return (
                <TableRegisterField
                    {...fieldProps}
                    field={field}
                    startIcon={
                        <ArrowState
                            width="20px"
                            height="20px"
                        />
                    }
                />
            );
        }
        case ColumnType.HINT:
            return (
                <HintRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        case ColumnType.RICH_TEXT: {
            return (
                <RTEditorRegisterField
                    {...fieldProps}
                    field={field}
                    sectionHeaderRef={sectionHeaderRef}
                />
            );
        }
        case ColumnType.HYPERLINK: {
            return (
                <HyperlinkField
                    {...fieldProps}
                    field={field}
                />
            );
        }
        case ColumnType.DATETIME_FORMULA: {
            return (
                <DateFormulaField
                    {...fieldProps}
                    field={field}
                />
            );
        }
        case ColumnType.SIMPLE_FORMULA: {
            return (
                <SimpleFormulaField
                    {...fieldProps}
                    field={field}
                />
            );
        }
        case ColumnType.COMPLEX_FORMULA: {
            return (
                <ComplexFormulaField
                    {...fieldProps}
                    field={field}
                />
            );
        }
        case ColumnType.STRING_FORMULA: {
            return (
                <ComplexFormulaField
                    {...fieldProps}
                    field={field}
                />
            );
        }
        case ColumnType.IMAGES: {
            return (
                <ImageGalleryRegisterField
                    formFieldProps={fieldProps}
                    field={field}
                />
            );
        }
        // case ColumnType.REPORT_ATTACHMENT:
        case ColumnType.ATTACHMENT: {
            return (
                <AttachmentRegisterField
                    formFieldProps={fieldProps}
                    field={field}
                />
            );
        }
        case ColumnType.GPS_POSITION: {
            return (
                <GPSPositionRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        }
        case ColumnType.RISK_MATRIX: {
            return (
                <RiskMatrixRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        }
        case ColumnType.WORKLOG: {
            return (
                <WorkLogRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        }
        case ColumnType.LINKED_TO: {
            const relatedLinks = getConstraint(field.constraintProperties, 'property-allow-related');
            if (relatedLinks === 'true') {
                return (
                    <RelatedLinksRegisterField
                        {...fieldProps}
                        field={field}
                    />
                );
            }

            return (
                <LinkedToRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        }
        case ColumnType.CUSTOM_ID: {
            return (
                <CustomIdRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        }
        case ColumnType.CENTRAL_LIBRARY: {
            return (
                <CentralLibraryRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        }
        case ColumnType.CONTROL: {
            return (
                <ControlRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        }
        case ColumnType.RISK_CAUSE: {
            return (
                <SingleSelectRiskCauseRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        }
        case ColumnType.RISK_EVENT: {
            return (
                <SingleSelectRiskEventRegisterField
                    {...fieldProps}
                    field={field}
                />
            );
        }
        default:
            return <UnsupportedRegisterField {...fieldProps} />;
    }
};

export default RegisterField;
