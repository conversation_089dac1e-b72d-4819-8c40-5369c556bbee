import React from 'react';
import MultiSelector<PERSON>ickerField from 'common/components/Form/FormFields/MultiSelectorPickerField';
import { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import { SectionFieldMetaData } from '@protecht/ui-library/library/types';
import { SelectorType } from 'common/types';
import useMSLibraryFieldsConditionalFilterRules from 'register/hooks/useMSLibraryFieldsConditionalFilterRules/useMSLibraryFieldsConditionalFilterRules';
import { LibrarySelectorEntriesColDef, DEFAULT_ORDER_FIELD, DEFAULT_SEARCH_FIELD } from 'library/components/AuditQuestion/AuditQuestionDefinitions';
import useAuditQuestionInfoBubbleData from 'register/hooks/useAuditQuestionInfoBubbleData';
import { getConstraint } from '../utils';
import MSLTableFieldView from './MSLTableFieldView';
import { RegisterFieldViewContextType } from 'view/types';
import { useAuditQuestionLoader } from 'library/components/AuditQuestion/useAuditQuestionLoader';

const AuditQuestionRegisterField: React.FC<FormFieldBaseProps & { field: SectionFieldMetaData }> = ({ field, ...fieldProps }) => {
    const filterData = useMSLibraryFieldsConditionalFilterRules(field, LibrarySelectorEntriesColDef);
    const { prefetchOnHover } = useAuditQuestionInfoBubbleData();

    const tableView = getConstraint(field.constraintProperties, 'table-view') === 'true';
    const tableHeight = getConstraint(field.constraintProperties, 'table-height');

    const { handleParamsChange, ...loaderResult } = useAuditQuestionLoader();

    if (tableView) {
        return (
            <MSLTableFieldView
                {...fieldProps}
                colDef={LibrarySelectorEntriesColDef}
                defaultOrderField={DEFAULT_ORDER_FIELD}
                defaultSearchField={DEFAULT_SEARCH_FIELD}
                filterData={filterData}
                loaderResult={loaderResult}
                onParamsChange={handleParamsChange}
                viewContext={RegisterFieldViewContextType.AuditQuestion}
                tableHeight={tableHeight}
                type={SelectorType.AUDIT_QUESTION}
                identityColumnName="question"
            />
        );
    }

    return (
        <MultiSelectorPickerField
            {...fieldProps}
            filterData={filterData}
            type={SelectorType.AUDIT_QUESTION}
            onHover={prefetchOnHover}
        />
    );
};

export default AuditQuestionRegisterField;
