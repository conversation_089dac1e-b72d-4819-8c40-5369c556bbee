import React, { useCallback, useEffect, useMemo, useState } from 'react';
import MultiSelectorPickerField from 'common/components/Form/FormFields/MultiSelectorPickerField';
import { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import { SectionFieldMetaData } from '@protecht/ui-library/library/types';
import { SelectorType } from 'common/types';
import { UserFilterData } from 'library/components/User/UserLayout';
import useFormContext from 'common/hooks/forms/useFormContext';
import useFieldConditionalRules from 'rules/hooks/useFieldConditionalRules';
import { UserColDef, DEFAULT_ORDER_FIELD, DEFAULT_SEARCH_FIELD } from 'library/components/User/UserDefinitions';
import { getConstraint } from '../utils';
import MSLTableFieldView from './MSLTableFieldView';
import { RegisterFieldViewContextType } from 'view/types';
import { useUserLoader } from 'library/components/User/useUserLoader';

const UserRegisterField: React.FC<FormFieldBaseProps & { field: SectionFieldMetaData }> = ({ field, ...fieldProps }) => {
    const { watch, trigger } = useFormContext();
    const { columnName } = field;

    const { sourceFilterByField, isExactMatch, filteredField } = useFieldConditionalRules(field.columnName)?.filter ?? {};

    const [filterData, setFilterData] = useState<UserFilterData>();
    const [filterDataUpdated, setFilterDataUpdated] = useState(false);

    const sourceFieldValue = sourceFilterByField ? watch(sourceFilterByField) : undefined;

    const sourceFilterByFieldValue = useMemo(() => {
        if (sourceFilterByField) {
            return Array.isArray(sourceFieldValue) ? sourceFieldValue[0] : sourceFieldValue;
        }
        return '';
    }, [sourceFieldValue, sourceFilterByField]);

    useEffect(() => {
        if (filterDataUpdated) {
            void trigger(columnName);
            setFilterDataUpdated(false);
        }
    }, [columnName, filterDataUpdated, setFilterDataUpdated, trigger]);

    const triggerUserRules = useCallback(() => {
        if (sourceFilterByField && sourceFilterByFieldValue && sourceFilterByFieldValue !== '' && filteredField) {
            setFilterData({
                filter: {
                    filterValue: sourceFilterByFieldValue?.[filteredField] || sourceFilterByFieldValue,
                    isExactMatch: isExactMatch ?? false,
                    filteredField: filteredField,
                },
            });
            setFilterDataUpdated(true);
        } else {
            setFilterData(undefined);
        }
    }, [sourceFilterByField, sourceFilterByFieldValue, filteredField, isExactMatch]);

    useEffect(() => {
        triggerUserRules();
    }, [triggerUserRules]);

    const tableView = getConstraint(field.constraintProperties, 'table-view') === 'true';
    const tableHeight = getConstraint(field.constraintProperties, 'table-height');

    const { handleParamsChange, ...loaderResult } = useUserLoader();

    if (tableView) {
        return (
            <MSLTableFieldView
                {...fieldProps}
                colDef={UserColDef}
                defaultOrderField={DEFAULT_ORDER_FIELD}
                defaultSearchField={DEFAULT_SEARCH_FIELD}
                filterData={filterData}
                loaderResult={loaderResult}
                onParamsChange={handleParamsChange}
                viewContext={RegisterFieldViewContextType.Users}
                tableHeight={tableHeight}
                type={SelectorType.USER}
                identityColumnName="name"
            />
        );
    }

    return (
        <MultiSelectorPickerField
            {...fieldProps}
            filterData={filterData}
            type={SelectorType.USER}
        />
    );
};

export default UserRegisterField;
