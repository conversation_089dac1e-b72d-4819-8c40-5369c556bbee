import Box from '@mui/material/Box';
import { GridColDef } from '@mui/x-data-grid';
import FormField, { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import { IdOnly, SYSTEM_COLUMN } from '@protecht/ui-library/library/types';
import { ViewExpressionRest } from 'api/generated/types';
import { SelectorType } from 'common/types';
import LibraryEntriesTableFieldView from 'library/components/shared/LibraryEntriesTableFieldView';
import { LibraryLoaderResult, LibraryTableParamsCallback } from 'library/types';
import React, { useMemo } from 'react';
import { useRegisterEntryContext } from 'register/context/RegisterEntryContext';
import { RegisterEntryRecord, RegisterEntryRest } from 'register/types';
import { getRegisterEntryField } from 'register/utils';
import { ExpressionType, RegisterFieldViewContextType } from 'view/types';

const DEFAULT_TABLE_HEIGHT = 200;

const apiExpressions = (selectedEntries: IdOnly[]) => {
    const expressions: ViewExpressionRest[] = [];

    expressions.push({
        id: 0,
        expression: ExpressionType.IN,
        property: SYSTEM_COLUMN.ID,
        type: 'STRING',
        value: selectedEntries.length > 0 ? selectedEntries.map((entry) => entry.id).join(';') : '',
    });

    return expressions;
};

type Props<T extends IdOnly> = FormFieldBaseProps & {
    colDef: GridColDef[];
    defaultOrderField: string;
    defaultSearchField: string;
    filterData?: any;
    identityColumnName: string;
    loaderResult: LibraryLoaderResult<T>;
    viewContext: RegisterFieldViewContextType;
    tableHeight?: number;
    type: SelectorType;
    onParamsChange: LibraryTableParamsCallback;
};

const MSLTableFieldView = <T extends IdOnly>({
    colDef,
    defaultOrderField,
    defaultSearchField,
    filterData,
    loaderResult,
    readOnly,
    viewContext,
    tableHeight,
    type,
    identityColumnName,
    onParamsChange,
    ...formFieldProps
}: Props<T>) => {
    return (
        <FormField
            {...formFieldProps}
            readOnly={readOnly}
            hideFieldLabel
            renderField={({ ref: _ref, value, onChange }) => {
                return (
                    <RenderField
                        colDef={colDef}
                        defaultOrderField={defaultOrderField}
                        defaultSearchField={defaultSearchField}
                        filterData={filterData}
                        loaderResult={loaderResult}
                        value={value}
                        viewContext={viewContext}
                        tableHeight={tableHeight}
                        type={type}
                        onChange={onChange}
                        label={formFieldProps.label}
                        tooltip={formFieldProps.tooltip}
                        identityColumnName={identityColumnName}
                        name={formFieldProps.name}
                        onParamsChange={onParamsChange}
                        readOnly={readOnly}
                    />
                );
            }}
        />
    );
};

type RenderFieldProps<T extends IdOnly> = Props<T> & {
    value: T[];
    onChange: (currentSelection: T[]) => void;
};

const RenderField = <T extends IdOnly>({
    colDef,
    defaultOrderField,
    defaultSearchField,
    loaderResult,
    type,
    value,
    viewContext,
    onChange,
    label,
    tooltip,
    filterData,
    tableHeight,
    identityColumnName,
    name,
    onParamsChange,
    readOnly,
}: RenderFieldProps<T>) => {
    const apiExpressionsList = useMemo(() => apiExpressions(value ?? []), [value]);

    const tableHeightValue = isNaN(Number(tableHeight)) ? DEFAULT_TABLE_HEIGHT : Number(tableHeight);
    const { entry: entryItem, register } = useRegisterEntryContext();

    const entry = entryItem as RegisterEntryRecord;
    const entryField = getRegisterEntryField(entry.record as RegisterEntryRest, name);

    const context = `multiSelect${viewContext}_TableId-${register!.id}_ColumnId-${entryField?.fieldId}`;

    return (
        <Box sx={{ height: `${tableHeightValue}px`, width: '100%', overflow: 'hidden' }}>
            <LibraryEntriesTableFieldView
                additionalExpressions={apiExpressionsList}
                filterData={filterData}
                loaderResult={loaderResult}
                onParamsChange={onParamsChange}
                colDef={colDef}
                defaultOrderField={defaultOrderField}
                defaultSearchField={defaultSearchField}
                selected={value ?? []}
                type={type}
                viewContext={context}
                onChange={onChange}
                label={label}
                labelTooltip={tooltip}
                identityColumnName={identityColumnName}
                readOnly={readOnly}
            />
        </Box>
    );
};

export default MSLTableFieldView;
