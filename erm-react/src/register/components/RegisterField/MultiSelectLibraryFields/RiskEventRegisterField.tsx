import React from 'react';
import MultiSelectorPickerField from 'common/components/Form/FormFields/MultiSelectorPickerField';
import { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import { SectionFieldMetaData } from '@protecht/ui-library/library/types';
import { SelectorType } from 'common/types';
import useMSLibraryFieldsConditionalFilterRules from 'register/hooks/useMSLibraryFieldsConditionalFilterRules/useMSLibraryFieldsConditionalFilterRules';
import { RiskEventColDef, DEFAULT_ORDER_FIELD, DEFAULT_SEARCH_FIELD } from 'library/components/RiskEvent/RiskEventDefinitions';
import useRiskEventInfoBubbleData from 'register/hooks/useRiskEventInfoBubbleData';
import { getConstraint } from '../utils';
import MSLTableFieldView from './MSLTableFieldView';
import { RegisterFieldViewContextType } from 'view/types';
import { useRiskEventLoader } from 'library/components/RiskEvent/useRiskEventLoader';

const RiskEventRegisterField: React.FC<FormFieldBaseProps & { field: SectionFieldMetaData }> = ({ field, ...fieldProps }) => {
    const filterData = useMSLibraryFieldsConditionalFilterRules(field, RiskEventColDef);
    const { prefetchOnHover } = useRiskEventInfoBubbleData();

    const tableView = getConstraint(field.constraintProperties, 'table-view') === 'true';
    const tableHeight = getConstraint(field.constraintProperties, 'table-height');

    const { handleParamsChange, ...loaderResult } = useRiskEventLoader();

    if (tableView) {
        return (
            <MSLTableFieldView
                {...fieldProps}
                colDef={RiskEventColDef}
                defaultOrderField={DEFAULT_ORDER_FIELD}
                defaultSearchField={DEFAULT_SEARCH_FIELD}
                filterData={filterData}
                loaderResult={loaderResult}
                onParamsChange={handleParamsChange}
                viewContext={RegisterFieldViewContextType.RiskEvents}
                tableHeight={tableHeight}
                type={SelectorType.RISK_EVENT}
                identityColumnName="name"
            />
        );
    }

    return (
        <MultiSelectorPickerField
            {...fieldProps}
            filterData={filterData}
            type={SelectorType.RISK_EVENT}
            persistViewState={false}
            onHover={prefetchOnHover}
        />
    );
};

export default RiskEventRegisterField;
