import React from 'react';
import MultiSelector<PERSON>ickerField from 'common/components/Form/FormFields/MultiSelectorPickerField';
import { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import { SectionFieldMetaData } from '@protecht/ui-library/library/types';
import { SelectorType } from 'common/types';
import useMSLibraryFieldsConditionalFilterRules from 'register/hooks/useMSLibraryFieldsConditionalFilterRules/useMSLibraryFieldsConditionalFilterRules';
import { LibrarySelectorEntriesColDef, DEFAULT_ORDER_FIELD, DEFAULT_SEARCH_FIELD } from 'library/components/ComplianceQuestion/ComplianceQuestionDefinitions';
import useComplianceQuestionInfoBubbleData from 'register/hooks/useComplianceQuestionInfoBubbleData';
import { getConstraint } from '../utils';
import MSLTableFieldView from './MSLTableFieldView';
import { RegisterFieldViewContextType } from 'view/types';
import { useComplianceQuestionLoader } from 'library/components/ComplianceQuestion/useComplianceQuestionLoader';

const ComplianceQuestionRegisterField: React.FC<FormFieldBaseProps & { field: SectionFieldMetaData }> = ({ field, ...fieldProps }) => {
    const filterData = useMSLibraryFieldsConditionalFilterRules(field, LibrarySelectorEntriesColDef);
    const { prefetchOnHover } = useComplianceQuestionInfoBubbleData();

    const tableView = getConstraint(field.constraintProperties, 'table-view') === 'true';
    const tableHeight = getConstraint(field.constraintProperties, 'table-height');

    const { handleParamsChange, ...loaderResult } = useComplianceQuestionLoader();

    if (tableView) {
        return (
            <MSLTableFieldView
                {...fieldProps}
                colDef={LibrarySelectorEntriesColDef}
                defaultOrderField={DEFAULT_ORDER_FIELD}
                defaultSearchField={DEFAULT_SEARCH_FIELD}
                filterData={filterData}
                loaderResult={loaderResult}
                onParamsChange={handleParamsChange}
                viewContext={RegisterFieldViewContextType.Question}
                tableHeight={tableHeight}
                type={SelectorType.QUESTION}
                identityColumnName="name"
            />
        );
    }

    return (
        <MultiSelectorPickerField
            {...fieldProps}
            filterData={filterData}
            type={SelectorType.QUESTION}
            onHover={prefetchOnHover}
        />
    );
};

export default ComplianceQuestionRegisterField;
