import React from 'react';
import MultiSelector<PERSON>ickerField from 'common/components/Form/FormFields/MultiSelectorPickerField';
import { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import { SectionFieldMetaData } from '@protecht/ui-library/library/types';
import { SelectorType } from 'common/types';
import useMSLibraryFieldsConditionalFilterRules from 'register/hooks/useMSLibraryFieldsConditionalFilterRules/useMSLibraryFieldsConditionalFilterRules';
import { LibrarySelectorEntriesColDef, DEFAULT_ORDER_FIELD, DEFAULT_SEARCH_FIELD } from 'library/components/Bowtie/BowtieDefinitions';
import useBowtieInfoBubbleData from 'register/hooks/useBowtieInfoBubbleData';
import { getConstraint } from '../utils';
import MSLTableFieldView from './MSLTableFieldView';
import { RegisterFieldViewContextType } from 'view/types';
import { useBowtieLoader } from 'library/components/Bowtie/useBowtieLoader';

const BowtieRegisterField: React.FC<FormFieldBaseProps & { field: SectionFieldMetaData }> = ({ field, ...fieldProps }) => {
    const filterData = useMSLibraryFieldsConditionalFilterRules(field, LibrarySelectorEntriesColDef);
    const { prefetchOnHover } = useBowtieInfoBubbleData();

    const tableView = getConstraint(field.constraintProperties, 'table-view') === 'true';
    const tableHeight = getConstraint(field.constraintProperties, 'table-height');

    const { handleParamsChange, ...loaderResult } = useBowtieLoader();

    if (tableView) {
        return (
            <MSLTableFieldView
                {...fieldProps}
                colDef={LibrarySelectorEntriesColDef}
                defaultOrderField={DEFAULT_ORDER_FIELD}
                defaultSearchField={DEFAULT_SEARCH_FIELD}
                filterData={filterData}
                loaderResult={loaderResult}
                onParamsChange={handleParamsChange}
                viewContext={RegisterFieldViewContextType.Bowtie}
                tableHeight={tableHeight}
                type={SelectorType.BOWTIE}
                identityColumnName="name"
            />
        );
    }

    return (
        <MultiSelectorPickerField
            {...fieldProps}
            filterData={filterData}
            type={SelectorType.BOWTIE}
            onHover={prefetchOnHover}
        />
    );
};

export default BowtieRegisterField;
