import React from 'react';
import MultiSelector<PERSON>ickerField from 'common/components/Form/FormFields/MultiSelectorPickerField';
import { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import { SectionFieldMetaData } from '@protecht/ui-library/library/types';
import { SelectorType } from 'common/types';
import useMSLibraryFieldsConditionalFilterRules from 'register/hooks/useMSLibraryFieldsConditionalFilterRules/useMSLibraryFieldsConditionalFilterRules';
import { RiskCauseColDef, DEFAULT_ORDER_FIELD, DEFAULT_SEARCH_FIELD } from 'library/components/RiskCause/RiskCauseDefinitions';
import useRiskCauseInfoBubbleData from 'register/hooks/useRiskCauseInfoBubbleData';
import { getConstraint } from '../utils';
import MSLTableFieldView from './MSLTableFieldView';
import { RegisterFieldViewContextType } from 'view/types';
import { useRiskCauseLoader } from 'library/components/RiskCause/useRiskCauseLoader';

const RiskCauseRegisterField: React.FC<FormFieldBaseProps & { field: SectionFieldMetaData }> = ({ field, ...fieldProps }) => {
    const filterData = useMSLibraryFieldsConditionalFilterRules(field, RiskCauseColDef);
    const { prefetchOnHover } = useRiskCauseInfoBubbleData();

    const tableView = getConstraint(field.constraintProperties, 'table-view') === 'true';
    const tableHeight = getConstraint(field.constraintProperties, 'table-height');

    const { handleParamsChange, ...loaderResult } = useRiskCauseLoader();

    if (tableView) {
        return (
            <MSLTableFieldView
                {...fieldProps}
                colDef={RiskCauseColDef}
                defaultOrderField={DEFAULT_ORDER_FIELD}
                defaultSearchField={DEFAULT_SEARCH_FIELD}
                filterData={filterData}
                loaderResult={loaderResult}
                onParamsChange={handleParamsChange}
                viewContext={RegisterFieldViewContextType.RiskCauses}
                tableHeight={tableHeight}
                type={SelectorType.RISK_CAUSE}
                identityColumnName="name"
            />
        );
    }

    return (
        <MultiSelectorPickerField
            {...fieldProps}
            filterData={filterData}
            type={SelectorType.RISK_CAUSE}
            persistViewState={false}
            onHover={prefetchOnHover}
        />
    );
};

export default RiskCauseRegisterField;
