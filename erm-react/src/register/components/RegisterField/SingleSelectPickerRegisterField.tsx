import React from 'react';
import { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import { DataGridColDef, IdOnly, SectionFieldMetaData } from '@protecht/ui-library/library/types';
import InputSelectorPickerField from 'common/components/Form/FormFields/InputSelectorPickerField/InputSelectorPickerField';
import { getConstraint } from './utils';
import { IdWithNameAndStatusRest } from 'app/types';
import useFormContext from 'common/hooks/forms/useFormContext';
import ExtendedViewField from './ExtendedViewField';
import { SelectorType } from 'common/types';
import { RegisterFieldViewContextType } from 'view/types';
import { ColumnType } from 'register/types';
import { LibraryLoaderResult, LibraryTableParamsCallback } from 'library/types';

type Props<T extends IdOnly> = FormFieldBaseProps & {
    field: SectionFieldMetaData;
    colDef: DataGridColDef[];
    filterData?: any;
    onDataLoad?: (query: string) => Promise<IdWithNameAndStatusRest[]>;
    onDataClear?: () => void;

    loaderResult: LibraryLoaderResult<T>;
    onParamsChange: LibraryTableParamsCallback;
};

const DEFAULT_IDENTITY_COLUMN_NAME = 'name';
const DEFAULT_SEARCH_FIELD = 'name';
const DEFAULT_ORDER_FIELD = 'name';

const SingleSelectPickerRegisterField = <T extends IdOnly>({
    field,
    loaderResult,
    onParamsChange,
    filterData,
    onDataLoad,
    onDataClear,
    colDef,
    ...fieldProps
}: Props<T>) => {
    const isExtendedView: boolean = getConstraint(field?.constraintProperties, 'extendedView') === 'true';
    const placeholder: string = getConstraint(field?.constraintProperties, 'emptyMessage');

    const { watch } = useFormContext();
    const selectedItems: IdWithNameAndStatusRest[] = watch(fieldProps.name);

    if (isExtendedView && field.columnType !== ColumnType.RISK_CAUSE) {
        return (
            <ExtendedViewField
                {...fieldProps}
                colDef={colDef}
                type={field.columnType as unknown as SelectorType}
                label={field.label}
                tooltip={fieldProps.tooltip}
                identityColumnName={DEFAULT_IDENTITY_COLUMN_NAME}
                defaultOrderField={DEFAULT_ORDER_FIELD}
                defaultSearchField={DEFAULT_SEARCH_FIELD}
                filterData={filterData}
                readOnly={fieldProps.readOnly}
                loaderResult={loaderResult}
                onParamsChange={onParamsChange}
                viewContext={RegisterFieldViewContextType.RiskEvents}
            />
        );
    }

    return (
        <InputSelectorPickerField<IdWithNameAndStatusRest>
            {...fieldProps}
            placeholder={placeholder}
            selectedItems={selectedItems || []}
            type={field.columnType}
            onDataLoad={onDataLoad}
            onDataClear={onDataClear}
            preferArrayForSingleSelect={true}
            filterData={filterData}
        />
    );
};

export default SingleSelectPickerRegisterField;
