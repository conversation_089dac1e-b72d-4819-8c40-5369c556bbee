import React, { useCallback, useMemo, useState, useEffect } from 'react';
import Grid from '@mui/material/Grid';
import InputField from '@protecht/ui-library/library/components/FormFields/InputField';
import IconButton from '@protecht/ui-library/library/components/IconButton';
import { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import { CurrencySymbolPlacement, SectionFieldMetaData } from '@protecht/ui-library/library/types';
import { Calculate } from '@protecht/ui-library/library/components/SVGIcons';
import { useFormContext } from 'react-hook-form';
import { getConstraint } from '../utils';
import { useGetSystemConfigurationQuery } from 'app/rtkApi';
import { getCurrencySymbolPlacement } from '@protecht/ui-library/library/components/CurrencyInput';
import { strings } from 'common/utils/i18n';
import ErrorDialog from '../SimpleDateFormulaField/SimpleDateFormulaDialog';
import { ColumnType } from '@protecht/ui-library/library/types';
import { useFormulaFieldEvaluation } from 'register/hooks/useFormulaFieldEvaluation';

const ComplexFormulaField: React.FC<FormFieldBaseProps & { field: SectionFieldMetaData }> = ({ field, readOnly, ...fieldProps }) => {
    const { columnName, columnType } = field;
    const fieldName = fieldProps.name;

    const { data: systemConfiguration } = useGetSystemConfigurationQuery();

    const { currencyLocale, numericScale, formatType, defaultValue } = useMemo(() => {
        const formatType = getConstraint(field.constraintProperties, 'number-format') || 'Currency';
        const locale = getConstraint(field.constraintProperties, 'currency-locale') || (systemConfiguration ? systemConfiguration.currency_default : '') || '';
        const defaultValue = getConstraint(field.constraintProperties, 'default-value') || '';
        const scale = getConstraint(field.constraintProperties, 'numeric_scale') || '';
        return { currencyLocale: locale, numericScale: scale, formatType, defaultValue };
    }, [field.constraintProperties, systemConfiguration]);

    const { setValue, watch } = useFormContext();
    const currentValue = watch(fieldName);
    const [displayValue, setDisplayValue] = useState('');

    const getFormattedValue = useCallback(
        (value: string): string => {
            const numericValue = Number(value);
            const formattedNumber = numericValue.toFixed(numericScale);

            if (formatType === 'Currency') {
                const localeParts = currencyLocale.split('_');
                const currencySymbol = localeParts[2] || '$';
                const placement = getCurrencySymbolPlacement(currencySymbol);

                return placement === CurrencySymbolPlacement.Prefix ? `${currencySymbol} ${formattedNumber}` : `${formattedNumber} ${currencySymbol}`;
            } else if (formatType === 'Percentage') {
                return formattedNumber + '%';
            }
            return formattedNumber;
        },
        [currencyLocale, numericScale, formatType],
    );

    useEffect(() => {
        if ((currentValue === undefined || currentValue === null || currentValue === '') && defaultValue !== '') {
            setValue(fieldName, defaultValue, { shouldValidate: false, shouldDirty: false });

            setDisplayValue(columnType === ColumnType.STRING_FORMULA ? defaultValue : getFormattedValue(defaultValue));
        }
    }, [currentValue, defaultValue, fieldName, setValue, getFormattedValue, columnType]);

    useEffect(() => {
        if (currentValue !== undefined && currentValue !== null && currentValue !== '') {
            const formatted = columnType === ColumnType.STRING_FORMULA ? String(currentValue) : getFormattedValue(String(currentValue));
            setDisplayValue(formatted);
        } else {
            setDisplayValue('');
        }
    }, [currentValue, getFormattedValue, columnType]);

    const onEvaluationSuccess = useCallback((formattedValue: string) => {
        setDisplayValue(formattedValue);
    }, []);

    const { evaluate, openDialog, setOpenDialog, errorReason } = useFormulaFieldEvaluation({
        columnName: columnName,
        fieldName: fieldName,
        fieldLabel: field.label,
        displayFormattingCallback: (value: string) => (columnType === ColumnType.STRING_FORMULA ? value : getFormattedValue(value)),
        onEvaluationSuccess: onEvaluationSuccess,
    });

    return (
        <>
            <Grid
                container
                spacing={1}
                alignItems="center"
            >
                <Grid
                    item
                    xs
                >
                    <InputField
                        {...fieldProps}
                        size="medium"
                        inputMode="text"
                        displayValue={displayValue}
                        placeholder={strings('common:formula.instruction')}
                        readOnly={true}
                        inputProps={{ tabIndex: -1 }}
                        inputAdornment={
                            !readOnly && (
                                <IconButton
                                    width="32px"
                                    height="32px"
                                    style={{ padding: '0px' }}
                                    color="primary"
                                    onClick={evaluate}
                                    data-testid="complex-formula"
                                >
                                    <Calculate
                                        height="22px"
                                        width="22px"
                                    />
                                </IconButton>
                            )
                        }
                    />
                </Grid>
            </Grid>

            {openDialog && (
                <ErrorDialog
                    visible={openDialog}
                    onClose={() => setOpenDialog(false)}
                    errorReason={errorReason}
                    fieldName={field.label || fieldName}
                />
            )}
        </>
    );
};

export default ComplexFormulaField;
