import React, { useMemo } from 'react';
import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import DialogActions from '@mui/material/DialogActions';

import { strings } from 'common/utils/i18n';
import { AlertType } from '@protecht/ui-library/library/types';
import Button, {
  ButtonStyles,
} from '@protecht/ui-library/library/components/Button';
import ConfirmationDialog from '@protecht/ui-library/library/components/ConfirmationDialog';

export type ErrorDialogProps = {
  visible?: boolean;
  onClose?: () => void;
  errorReason?: string;
  fieldName?: string;
};

const ListContainer = styled(Box)({
  display: 'flex',
  flexDirection: 'column',
  overflow: 'hidden',
});

// TODO: We need to create a new endpoint on the backend so that this can be done more elegantly.
// Until then, for testing purposes we’re using the internal endpoint.
const parseErrorReason = (
  rawReason: string,
  fieldName?: string
): string => {
  if (!rawReason) {
    return '';
  }

  // 1. Remove a duplicate "Failed to evaluate field [xx]." prefix, if present.
  if (fieldName) {
    const dupPattern = new RegExp(
      String.raw`^Failed to evaluate field \[${fieldName}\]\.?\s*`,
      'i'
    );
    rawReason = rawReason.replace(dupPattern, '');
  }

  // 2. Remove a leading "Reason:" label that some back‑end messages embed.
  rawReason = rawReason.replace(/^Reason:\s*/i, '');

  return rawReason.trim();
};

const ErrorDialog: React.FC<ErrorDialogProps> = ({
  visible = true,
  onClose,
  errorReason = '',
  fieldName = '',
}) => {
  const header = strings('common:formula.formulaHeader');
  const failedMessage = `${strings('common:formula.failed')} [${fieldName}]`;

  const cleanReason = useMemo(
    () => parseErrorReason(errorReason, fieldName),
    [errorReason, fieldName]
  );

  const additionalContent = (
    <ListContainer>
      <Typography
        sx={{ marginBottom: 2, whiteSpace: 'pre-wrap', fontWeight: 'normal' }}
      >
        {header}
      </Typography>
      <Typography sx={{ marginBottom: 1, whiteSpace: 'pre-wrap' }}>
        <span style={{ fontWeight: 'bold' }}>
          {strings('common:label.error')}:
        </span>{' '}
        {failedMessage}
      </Typography>
      {!!cleanReason && (
        <Typography sx={{ marginBottom: 1, whiteSpace: 'pre-wrap' }}>
          <span style={{ fontWeight: 'bold' }}>
            {strings('common:label.reason')}:
          </span>{' '}
          {cleanReason}
        </Typography>
      )}
    </ListContainer>
  );

  return (
    <ConfirmationDialog
      visible={visible}
      type={AlertType.Error}
      title={strings('common:formula.calculate')}
      message=""
      additionalContent={additionalContent}
      onClose={onClose}
      actions={
        <DialogActions>
          <Button
            {...ButtonStyles.dialogButton}
            dataTestId="button-confirm"
            onClick={onClose}
          >
            {strings('ermMessages:btn_okay')}
          </Button>
        </DialogActions>
      }
    />
  );
};

export default ErrorDialog;
