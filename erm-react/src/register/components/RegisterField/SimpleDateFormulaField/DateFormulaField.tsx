import React, { useCallback, useEffect, useState } from 'react';
import Grid from '@mui/material/Grid';
import InputField from '@protecht/ui-library/library/components/FormFields/InputField';
import IconButton from '@protecht/ui-library/library/components/IconButton';
import { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import { SectionFieldMetaData } from '@protecht/ui-library/library/types';
import { DateTime } from 'luxon';
import { Calculate } from '@protecht/ui-library/library/components/SVGIcons';
import { useFormContext } from 'react-hook-form';
import useUserDateFormat from 'common/hooks/useUserDateFormat';
import { strings } from 'common/utils/i18n';
import ErrorDialog from './SimpleDateFormulaDialog';
import { useFormulaFieldEvaluation } from 'register/hooks/useFormulaFieldEvaluation';

const DateFormulaField: React.FC<FormFieldBaseProps & { field: SectionFieldMetaData }> = ({ field, readOnly, ...fieldProps }) => {
    const { columnName } = field;
    const { watch } = useFormContext();
    const fieldName = fieldProps.name;

    const currentValue = watch(fieldName);
    const [displayValue, setDisplayValue] = useState('');
    const userDateFormat = useUserDateFormat();

    const displayFormattingCallback = useCallback((value: string) => {
        const dt = DateTime.fromISO(value);
        return dt.isValid ? dt.toFormat(userDateFormat) : value;
    }, [userDateFormat]);

    const onEvaluationSuccess = useCallback((formattedValue: string) => {
        setDisplayValue(formattedValue);
    }, []);

    const { evaluate, openDialog, setOpenDialog, errorReason } = useFormulaFieldEvaluation({
        columnName: columnName,
        fieldName: fieldName,
        fieldLabel: field.label,
        displayFormattingCallback: displayFormattingCallback,
        onEvaluationSuccess: onEvaluationSuccess,
    });

    useEffect(() => {
        if (currentValue !== undefined && currentValue !== null && currentValue !== '') {
            setDisplayValue(displayFormattingCallback(currentValue));
        } else {
            setDisplayValue('');
        }
    }, [currentValue, displayFormattingCallback]);

    return (
        <>
            <Grid
                container
                spacing={1}
                alignItems="center"
            >
                <Grid
                    item
                    xs
                >
                    <InputField
                        {...fieldProps}
                        size="medium"
                        inputMode="text"
                        displayValue={displayValue}
                        value={currentValue}
                        placeholder={strings('common:formula.instruction')}
                        readOnly={true}
                        inputProps={{ tabIndex: -1 }}
                        inputAdornment={
                            !readOnly && (
                                <IconButton
                                    width="32px"
                                    height="32px"
                                    style={{ padding: '0px' }}
                                    color="primary"
                                    onClick={evaluate}
                                    data-testid="date-formula"
                                >
                                    <Calculate
                                        height="22px"
                                        width="22px"
                                    />
                                </IconButton>
                            )
                        }
                    />
                </Grid>
            </Grid>
            {openDialog && (
                <ErrorDialog
                    visible={openDialog}
                    onClose={() => setOpenDialog(false)}
                    errorReason={errorReason}
                    fieldName={field.label || fieldName}
                />
            )}
        </>
    );
};

export default DateFormulaField;
