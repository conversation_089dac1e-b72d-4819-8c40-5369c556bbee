import React, { useCallback, useMemo, useState, useEffect } from 'react';
import Grid from '@mui/material/Grid';
import InputField from '@protecht/ui-library/library/components/FormFields/InputField';
import IconButton from '@protecht/ui-library/library/components/IconButton';
import { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import { CurrencySymbolPlacement, SectionFieldMetaData } from '@protecht/ui-library/library/types';
import { Calculate } from '@protecht/ui-library/library/components/SVGIcons';
import { useFormContext } from 'react-hook-form';
import { getConstraint } from '../utils';
import { useGetSystemConfigurationQuery } from 'app/rtkApi';
import { getCurrencySymbolPlacement } from '@protecht/ui-library/library/components/CurrencyInput';
import { strings } from 'common/utils/i18n';
import ErrorDialog from './SimpleDateFormulaDialog';
import { useFormulaFieldEvaluation } from 'register/hooks/useFormulaFieldEvaluation';

const SimpleFormulaField: React.FC<FormFieldBaseProps & { field: SectionFieldMetaData }> = ({ field, readOnly, ...fieldProps }) => {
    const { columnName } = field;
    const fieldName = fieldProps.name;

    const { data: systemConfiguration } = useGetSystemConfigurationQuery();

    const { currencyLocale, numericScale, formatType } = useMemo(() => {
        const formatType = getConstraint(field.constraintProperties, 'number-format') || 'Currency';
        const locale = getConstraint(field?.constraintProperties, 'currency-locale') || (systemConfiguration ? systemConfiguration.currency_default : '') || '';
        const scale = getConstraint(field.constraintProperties, 'numeric_scale') || '';
        return { currencyLocale: locale, numericScale: scale, formatType };
    }, [field.constraintProperties, systemConfiguration]);

    const { watch } = useFormContext();
    const currentValue = watch(fieldName);
    const [displayValue, setDisplayValue] = useState('');

    const getFormattedValue = useCallback(
        (value: string): string => {
            const numericValue = Number(value);
            const formattedNumber = numericValue.toFixed(numericScale);

            if (formatType === 'Currency') {
                const localeParts = currencyLocale.split('_');
                const currencySymbol = localeParts[2] || '$';
                const placement = getCurrencySymbolPlacement(currencySymbol);

                return placement === CurrencySymbolPlacement.Prefix ? `${currencySymbol} ${formattedNumber}` : `${formattedNumber} ${currencySymbol}`;
            } else if (formatType === 'Percentage') {
                return formattedNumber + '%';
            }
            return formattedNumber;
        },
        [currencyLocale, numericScale, formatType],
    );

    const onEvaluationSuccess = useCallback((formattedValue: string) => {
        setDisplayValue(formattedValue);
    }, []);

    const { evaluate, openDialog, setOpenDialog, errorReason } = useFormulaFieldEvaluation({
        columnName: columnName,
        fieldName: fieldName,
        fieldLabel: field.label,
        displayFormattingCallback: getFormattedValue,
        onEvaluationSuccess: onEvaluationSuccess,
    });

    useEffect(() => {
        if (currentValue !== undefined && currentValue !== null && currentValue !== '') {
            setDisplayValue(getFormattedValue(currentValue));
        } else {
            setDisplayValue('');
        }
    }, [currentValue, getFormattedValue]);

    return (
        <>
            <Grid
                container
                spacing={1}
                alignItems="center"
            >
                <Grid
                    item
                    xs
                >
                    <InputField
                        {...fieldProps}
                        size="medium"
                        inputMode="text"
                        displayValue={displayValue}
                        placeholder={strings('common:formula.instruction')}
                        readOnly={true}
                        inputProps={{ tabIndex: -1 }}
                        inputAdornment={
                            !readOnly && (
                                <IconButton
                                    width="32px"
                                    height="32px"
                                    style={{ padding: '0px' }}
                                    color="primary"
                                    onClick={evaluate}
                                    data-testid="simple-formula"
                                >
                                    <Calculate
                                        height="22px"
                                        width="22px"
                                    />
                                </IconButton>
                            )
                        }
                    />
                </Grid>
            </Grid>
            {openDialog && (
                <ErrorDialog
                    visible={openDialog}
                    onClose={() => setOpenDialog(false)}
                    errorReason={errorReason}
                    fieldName={field.label || fieldName}
                />
            )}
        </>
    );
};

export default SimpleFormulaField;
