import React from 'react';
import DialogActions from '@mui/material/DialogActions';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { strings } from 'common/utils/i18n';
import { AlertType } from '@protecht/ui-library/library/types';
import { sanitizeHTMLString } from '@protecht/ui-library/library/utils/string';

import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import ConfirmationDialog from '@protecht/ui-library/library/components/ConfirmationDialog';

type FormulaError = {
    fieldName: string;
    fieldLabel: string;
    errorMessage: string;
};

export type MultipleFormulaErrorDialogProps = {
    visible?: boolean;
    onClose?: () => void;
    errors?: FormulaError[];
};

const ListContainer = styled(Box)({
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
});

const formatErrorMessage = (msg: string) => {
  const safe = sanitizeHTMLString(msg);
  const parts = safe.split('<b>Reason');
  if (parts.length === 2) {
    const [errorPart, reasonPart] = parts;
    return (
      errorPart +
      '<br/>' +
      '<span style="display:block; margin-top:4px;"><b>Reason' +
      reasonPart
    );
  }
  return safe;
};

const MultipleFormulaErrorDialog = ({ visible = true, onClose, errors = [] }: MultipleFormulaErrorDialogProps) => {
    const header = strings('common:formula.formulaHeader');
    const title = strings('common:formula.calculate');

    const additionalContent = (
        <ListContainer>
            <Typography sx={{ marginBottom: 2, fontWeight: 'normal' }}>
                {header}
            </Typography>
            {errors.map((error, index) => (
                <Typography
                    key={index}
                    sx={{
                        whiteSpace: 'pre-line',
                        marginBottom: index < errors.length - 1 ? 2 : 0,
                    }}
                    dangerouslySetInnerHTML={{ __html: formatErrorMessage(error.errorMessage) }}
                />
            ))}
        </ListContainer>
    );

    return (
        <ConfirmationDialog
            visible={visible}
            type={AlertType.Error}
            title={title}
            message=""
            additionalContent={additionalContent}
            onClose={onClose}
            actions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        dataTestId="button-confirm"
                        onClick={onClose}
                    >
                        {strings('ermMessages:btn_okay')}
                    </Button>
                </DialogActions>
            }
        />
    );
};

export default MultipleFormulaErrorDialog;
