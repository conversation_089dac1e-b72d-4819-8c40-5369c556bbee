import { PackageContentDto } from 'marketplace/types';
import { groupRegistersByApplication } from 'marketplace/utils';
import { PackageModule } from 'marketplace/types';
import { ColumnType, RegisterRest, RegisterEntryRecord } from 'register/types';
import {
    getFieldLabel,
    createFieldMetadata,
    createSectiondMetadata,
    registerHasPermission,
    getRegisterEntryDisplayFieldValue,
    getFieldsByColumnType,
    registerHasUserFieldWithMessaging,
    registerHasAttachmentField,
    registerHasFormulaField,
    registerHasDueDateField,
    getEntryBreadcrumbs,
} from 'register/utils';
import { CORE_SECTION_LABEL } from '../constants';
import * as registerUtils from 'register/utils/utils';
import { PermissionCodes } from 'common/types';
import { RegisterDataRest, FieldRest, SectionRest, TableMetadataRest } from 'api/generated/types';
import { updateEntryRecord, mapValueToSimpleValue } from './utils';
import { Register } from 'api/generated/types';
import { RegisterPath } from 'register/routes';

// Mock generatePath function
jest.mock('react-router', () => ({
    ...jest.requireActual('react-router'),
    generatePath: jest.fn(),
}));

// Mock getListItems function
jest.mock('register/components/RegisterField/ListRegisterField/utils', () => ({
    ...jest.requireActual('register/components/RegisterField/ListRegisterField/utils'),
    getListItems: jest.fn(),
}));

import { generatePath } from 'react-router';
import { getListItems } from 'register/components/RegisterField/ListRegisterField/utils';

const mockedGeneratePath = generatePath as jest.MockedFunction<typeof generatePath>;
const mockedGetListItems = getListItems as jest.MockedFunction<typeof getListItems>;

describe('groupRegistersByApplication', () => {
    it('groups registers into modules by their applications', () => {
        const contentList: PackageContentDto[] = [
            {
                module: 'REGISTER',
                itemCount: 3,
                registerDetails: [
                    {
                        registerLabel: 'Showcase 1',
                        applicationName: 'App1',
                        recordCount: 0,
                        fieldsCount: 10,
                        existingApplication: true,
                        subtableDetails: [],
                        inConflict: false,
                        sectionDetails: [],
                        actionRegister: false,
                        forcedRegister: false,
                        scaleDetails: [],
                    },
                    {
                        registerLabel: 'Showcase 2',
                        applicationName: 'App1',
                        recordCount: 0,
                        fieldsCount: 11,
                        existingApplication: true,
                        subtableDetails: [],
                        inConflict: false,
                        sectionDetails: [],
                        actionRegister: false,
                        forcedRegister: false,
                        scaleDetails: [],
                    },
                    {
                        registerLabel: 'Showcase 3',
                        applicationName: 'App2',
                        recordCount: 0,
                        fieldsCount: 12,
                        existingApplication: true,
                        subtableDetails: [],
                        inConflict: false,
                        sectionDetails: [],
                        actionRegister: false,
                        forcedRegister: false,
                        scaleDetails: [],
                    },
                ],
                moduleIcon: 'fal fa-archive',
            },
        ];

        const result = groupRegistersByApplication(contentList);
        expect(result).toBeDefined();

        const applicationModules = result!.filter((content) => PackageModule[content.module!] === PackageModule.APPLICATION);
        expect(applicationModules).toHaveLength(2);

        const app1 = applicationModules.find((app) => app.appName === 'App1');
        expect(app1).not.toBeUndefined();
        expect(app1!.registerDetails).toHaveLength(2);

        const app2 = applicationModules.find((app) => app.appName === 'App2');
        expect(app2).not.toBeUndefined();
        expect(app2!.registerDetails).toHaveLength(1);
    });

    it('connect register data with register structure', () => {
        const contentList: PackageContentDto[] = [
            {
                module: 'REGISTER_DATA',
                itemCount: 12,
                registerDetails: [
                    {
                        registerLabel: 'Showcase 21-06',
                        recordCount: 12,
                        applicationName: 'test',
                        existingApplication: true,
                        subtableDetails: [],
                        fieldsCount: 5,
                        inConflict: false,
                        sectionDetails: [],
                        actionRegister: false,
                        forcedRegister: false,
                        scaleDetails: [],
                    },
                ],
                moduleIcon: 'fal fa-clipboard-list-check',
            },
            {
                module: 'REGISTER',
                itemCount: 1,
                registerDetails: [
                    {
                        registerLabel: 'Showcase 21-06',
                        applicationName: 'Roman Empire',
                        fieldsCount: 35,
                        recordCount: 12,
                        existingApplication: true,
                        subtableDetails: [],
                        inConflict: false,
                        sectionDetails: [],
                        actionRegister: false,
                        forcedRegister: false,
                        scaleDetails: [],
                    },
                ],
                moduleIcon: 'fal fa-archive',
            },
        ];

        const result = groupRegistersByApplication(contentList);
        expect(result).toBeDefined();

        const applicationModule = result!.find((content) => PackageModule[content.module!] === PackageModule.APPLICATION);
        const dataModule = result!.find((content) => PackageModule[content.module!] === PackageModule.REGISTER_DATA);

        expect(dataModule).not.toBeUndefined();
        expect(dataModule!.itemCount).toEqual(0);

        expect(applicationModule).not.toBeUndefined();
        const register = applicationModule!.registerDetails!.find((r) => r.registerLabel === 'Showcase 21-06');
        expect(register).not.toBeUndefined();

        expect(register!.recordCount).toEqual(12);
        expect(register!.fieldsCount).toEqual(35);
    });

    it('groups registers and their sub-tables into applications while ignoring Actions register', () => {
        const contentList: PackageContentDto[] = [
            {
                module: 'REGISTER',
                itemCount: 1,
                registerDetails: [
                    {
                        registerLabel: 'Showcase 21-06',
                        applicationName: 'Roman Empire',
                        recordCount: 0,
                        fieldsCount: 35,
                        existingApplication: true,
                        inConflict: false,
                        sectionDetails: [],
                        actionRegister: false,
                        forcedRegister: false,
                        scaleDetails: [],
                        subtableDetails: [
                            {
                                registerLabel: 'rrpriv',
                                applicationName: 'Roman Empire',
                                recordCount: 0,
                                fieldsCount: 2,
                                existingApplication: true,
                                subtableDetails: [],
                                inConflict: false,
                                sectionDetails: [],
                                actionRegister: false,
                                forcedRegister: false,
                                scaleDetails: [],
                            },
                            {
                                registerLabel: 'car brands',
                                applicationName: 'Roman Empire',
                                recordCount: 0,
                                fieldsCount: 8,
                                existingApplication: true,
                                subtableDetails: [],
                                inConflict: false,
                                sectionDetails: [],
                                actionRegister: false,
                                forcedRegister: false,
                                scaleDetails: [],
                            },
                            {
                                registerLabel: 'car brands',
                                applicationName: 'Roman Empire',
                                recordCount: 0,
                                fieldsCount: 8,
                                existingApplication: true,
                                subtableDetails: [],
                                inConflict: false,
                                sectionDetails: [],
                                actionRegister: false,
                                forcedRegister: false,
                                scaleDetails: [],
                            },
                            {
                                registerLabel: 'car brands',
                                applicationName: 'Roman Empire',
                                recordCount: 0,
                                fieldsCount: 8,
                                existingApplication: true,
                                subtableDetails: [],
                                inConflict: false,
                                sectionDetails: [],
                                actionRegister: false,
                                forcedRegister: false,
                                scaleDetails: [],
                            },
                            {
                                registerLabel: 'Actions',
                                applicationName: 'Roman Empire',
                                recordCount: 0,
                                fieldsCount: 8,
                                actionRegister: true,
                                forcedRegister: false,
                                existingApplication: true,
                                subtableDetails: [],
                                inConflict: false,
                                sectionDetails: [],
                                scaleDetails: [],
                            },
                            {
                                registerLabel: 'Forced Actions',
                                applicationName: 'Roman Empire',
                                recordCount: 0,
                                fieldsCount: 8,
                                actionRegister: true,
                                forcedRegister: true,
                                existingApplication: true,
                                subtableDetails: [],
                                inConflict: false,
                                sectionDetails: [],
                                scaleDetails: [],
                            },
                        ],
                    },
                ],
                moduleIcon: 'fal fa-archive',
            },
        ];

        const result = groupRegistersByApplication(contentList);
        expect(result).toBeDefined();

        const applicationModule = result!.find((content) => PackageModule[content.module!] === PackageModule.APPLICATION);

        expect(applicationModule).not.toBeUndefined();
        expect(applicationModule!.appName).toEqual('Roman Empire');
        expect(applicationModule!.registerDetails).toHaveLength(4);
        expect(applicationModule!.registerDetails!.find((r) => r.registerLabel === 'Showcase 21-06')).not.toBeUndefined();
        expect(applicationModule!.registerDetails!.find((r) => r.registerLabel === 'car brands')).not.toBeUndefined();
        expect(applicationModule!.registerDetails!.find((r) => r.registerLabel === 'rrpriv')).not.toBeUndefined();
        expect(applicationModule!.registerDetails!.find((r) => r.registerLabel === 'Forced Actions')).not.toBeUndefined();
        expect(applicationModule!.registerDetails!.find((r) => r.registerLabel === 'Actions')).toBeUndefined();
    });
});

describe('getFieldLabel', () => {
    it('should return the label if it exists', () => {
        const result = getFieldLabel(createFieldMetadata({ label: 'Label' }));
        expect(result).toEqual('Label');
    });

    it('should return the field label if it exists', () => {
        const result = getFieldLabel(createFieldMetadata({ fieldLabel: 'Field Label' }));
        expect(result).toEqual('Field Label');
    });

    it('should return the field label over label if both exist', () => {
        const result = getFieldLabel(createFieldMetadata({ label: 'Label', fieldLabel: 'Field Label' }));
        expect(result).toEqual('Field Label');
    });

    it('should return the Vendor Name label if in VRM module and field is in Core section with column type BUSINESS_UNIT', () => {
        jest.spyOn(registerUtils, 'isVrmModule').mockReturnValue(true);

        const result = getFieldLabel(
            createFieldMetadata({ label: 'Business Unit', columnType: ColumnType.BUSINESS_UNIT }),
            createSectiondMetadata({ label: CORE_SECTION_LABEL }),
        );
        expect(result).toEqual('Vendor Name');
        expect(result).not.toEqual('Business Unit');
    });

    it.each([
        { isVrmModule: false, columnType: ColumnType.BUSINESS_UNIT, sectionLabel: CORE_SECTION_LABEL },
        { isVrmModule: true, columnType: ColumnType.SINGLELINE_TEXT, sectionLabel: CORE_SECTION_LABEL },
        { isVrmModule: true, columnType: ColumnType.BUSINESS_UNIT, sectionLabel: 'Other Label' },
    ])(
        'should return the Business Unit label if not in VRM module or field is not in Core section or column type is not BUSINESS_UNIT',
        async ({ isVrmModule, columnType, sectionLabel }) => {
            jest.spyOn(registerUtils, 'isVrmModule').mockReturnValue(isVrmModule);

            const result = getFieldLabel(createFieldMetadata({ label: 'Business Unit', columnType }), createSectiondMetadata({ label: sectionLabel }));
            expect(result).toEqual('Business Unit');
            expect(result).not.toEqual('Vendor Name');
        },
    );
});

describe('registerHasPermission', () => {
    it('returns false when register not defined', () => {
        expect(registerHasPermission(PermissionCodes.REGISTER_DATA_ADD, undefined)).toBeFalsy();
    });

    it('returns false when register permissions are not defined', () => {
        expect(registerHasPermission(PermissionCodes.REGISTER_DATA_ADD, { id: 1234 })).toBeFalsy();
    });

    it('returns false when register permissions do not contain specified permissions', () => {
        expect(
            registerHasPermission(PermissionCodes.REGISTER_DATA_ADD, {
                id: 1234,
                userPermissionCodesForRegister: [PermissionCodes.REGISTER_DATA_EXPORT, PermissionCodes.REGISTER_DATA_VIEW_ALL],
            }),
        ).toBeFalsy();
    });

    it('returns true when register permissions contain specified permission', () => {
        expect(
            registerHasPermission(PermissionCodes.REGISTER_DATA_ADD, {
                id: 1234,
                userPermissionCodesForRegister: [PermissionCodes.REGISTER_DATA_EXPORT, PermissionCodes.REGISTER_DATA_ADD],
            }),
        ).toBeTruthy();
    });
});

describe('getRegisterEntryDisplayFieldValue', () => {
    const register = {
        id: 12345,
        label: 'Register title',
        identityColumn: {
            columnName: 'col_3',
            label: 'label3',
        },
    };
    const entry = {
        record: {
            label: 'test entry',
            id: 6789,
            sections: [
                {
                    editable: true,
                    label: 'test-section-1',
                    fields: [
                        {
                            label: 'label1',
                            fieldId: '1',
                            fieldName: 'col_1',
                        },
                        {
                            label: 'field2',
                            fieldId: '2',
                            fieldName: 'col_2',
                            parameters: {
                                displayValues: ['one', 'null', 'three'],
                            },
                            simpleValue: ['1', '2', '3'],
                        },
                    ],
                },
                {
                    editable: true,
                    label: 'test-section-2',
                    fields: [
                        {
                            label: 'label3',
                            fieldId: '3',
                            fieldName: 'col_3',
                            simpleValue: ['a', 'b', 'c'],
                        },
                        {
                            label: 'label4',
                            fieldId: '4',
                            fieldName: 'col_4',
                            parameters: {
                                displayValues: ['one', 'two', 'three'],
                            },
                            simpleValue: ['1', '2', '3'],
                        },
                    ],
                },
            ],
        },
    };
    it('returns entry id when identity field not available', () => {
        expect(getRegisterEntryDisplayFieldValue({ ...register, identityColumn: undefined }, entry)).toEqual('6789');
    });

    it('returns simple values when display values not available', () => {
        expect(getRegisterEntryDisplayFieldValue(register, entry)).toEqual('a, b, c');
    });

    it('returns display values when available', () => {
        expect(getRegisterEntryDisplayFieldValue({ ...register, identityColumn: { columnName: 'col_4', label: 'label4' } }, entry)).toEqual('one, two, three');
    });

    it('returns simple value when display value is null', () => {
        expect(getRegisterEntryDisplayFieldValue({ ...register, identityColumn: { columnName: 'col_2', label: 'label2' } }, entry)).toEqual('one, 2, three');
    });

    it('returns entry id when simple value for identity field not available', () => {
        expect(
            getRegisterEntryDisplayFieldValue({ ...register, identityColumn: { columnName: 'col_2', label: 'label2' } }, {
                ...entry,
                record: {
                    ...entry.record,
                    sections: [
                        {
                            editable: true,
                            label: 'test-section-1',
                            fields: [
                                {
                                    label: 'label1',
                                    fieldId: '1',
                                    fieldName: 'col_1',
                                },
                                {
                                    label: 'field2',
                                    fieldId: '2',
                                    fieldName: 'col_2',
                                },
                            ],
                        },
                    ],
                },
            } as RegisterDataRest),
        ).toEqual('6789');
    });
});

describe('permissionOf', () => {
    const REGISTER_OBJECT_IDENTITY = 10000;
    const REGISTER_APPLICATION = 'Registers';
    it('Returns permission format when a base code, application and object identity are passed in', () => {
        expect(registerUtils.permissionOf(PermissionCodes.REGISTER_DATA_ADD, REGISTER_APPLICATION, REGISTER_OBJECT_IDENTITY)).toEqual(
            `${PermissionCodes.REGISTER_DATA_ADD}_${REGISTER_APPLICATION}_${REGISTER_OBJECT_IDENTITY}`,
        );
    });

    const ACTIONS_OBJECT_IDENTITY = 10001;
    const TRANSITION_SUB_IDENTITY = 10420;
    const ACTIONS_APPLICATION = 'Actions';
    it('Returns permission format when a base code,  application, object identity and subidentity are passed in', () => {
        expect(
            registerUtils.permissionOf(PermissionCodes.REGISTER_DATA_TRANSITION, ACTIONS_APPLICATION, ACTIONS_OBJECT_IDENTITY, TRANSITION_SUB_IDENTITY),
        ).toEqual(`${PermissionCodes.REGISTER_DATA_TRANSITION}_${ACTIONS_APPLICATION}_${ACTIONS_OBJECT_IDENTITY}_${TRANSITION_SUB_IDENTITY}`);
    });
});

describe('mapValueToSimpleValue', () => {
    it('should return an empty array for empty arrays', () => {
        expect(mapValueToSimpleValue([])).toEqual([]);
    });

    it('should convert array of objects with id properties to array of id strings', () => {
        const input = [{ id: 1 }, { id: 2 }, { id: 3 }];
        expect(mapValueToSimpleValue(input)).toEqual(['1', '2', '3']);
    });

    it('should convert array of strings or numbers to array of strings', () => {
        expect(mapValueToSimpleValue(['a', 'b', 'c'])).toEqual(['a', 'b', 'c']);
        expect(mapValueToSimpleValue([1, 2, 3])).toEqual(['1', '2', '3']);
    });

    it('should return empty array for array of unsupported types', () => {
        expect(mapValueToSimpleValue([true, false])).toEqual([]);
    });

    it('should wrap string or number into an array', () => {
        expect(mapValueToSimpleValue('hello')).toEqual(['hello']);
        expect(mapValueToSimpleValue(42)).toEqual(['42']);
    });

    it('should extract id from object and return it as an array', () => {
        const input = { id: 99 };
        expect(mapValueToSimpleValue(input)).toEqual(['99']);
    });

    it('should return empty array for unsupported single values', () => {
        expect(mapValueToSimpleValue(true)).toEqual([]);
        expect(mapValueToSimpleValue(null)).toEqual([]);
        expect(mapValueToSimpleValue(undefined)).toEqual([]);
    });
});

describe('updateEntryRecord', () => {
    it('should return the original record if record or formValues are not provided', () => {
        const record = { sections: [] } as Register;
        expect(updateEntryRecord(record, undefined)).toBe(record);
        expect(updateEntryRecord(undefined, {})).toBeUndefined();
    });

    it('should update the record fields with simple values from formValues', () => {
        const record = {
            sections: [
                {
                    fields: [
                        { fieldName: 'name', simpleValue: [] },
                        { fieldName: 'age', simpleValue: [] },
                    ],
                },
            ],
        } as unknown as Register;

        const formValues = {
            name: 'John Doe',
            age: 30,
        };

        const updatedRecord = updateEntryRecord(record, formValues);

        expect(updatedRecord.sections).toBeDefined();
        const fields = updatedRecord?.sections![0].fields;

        expect(fields?.find((f) => f.fieldName === 'name')?.simpleValue).toEqual(['John Doe']);
        expect(fields?.find((f) => f.fieldName === 'age')?.simpleValue).toEqual(['30']);
    });

    it('should not update fields if formValues have empty or undefined values', () => {
        const record = {
            sections: [
                {
                    fields: [
                        { fieldName: 'name', simpleValue: ['Alice'] },
                        { fieldName: 'age', simpleValue: ['25'] },
                    ],
                },
            ],
        } as unknown as Register;

        const formValues = {
            name: '',
            age: undefined,
        };

        const updatedRecord = updateEntryRecord(record, formValues);

        expect(updatedRecord.sections).toBeDefined();
        const fields = updatedRecord.sections![0].fields;

        expect(fields?.find((f) => f.fieldName === 'name')?.simpleValue).toEqual(['']);
        expect(fields?.find((f) => f.fieldName === 'age')?.simpleValue).toEqual([]);
    });

    it('should handle nested sections and fields', () => {
        const record = {
            sections: [
                {
                    fields: [
                        { fieldName: 'name', simpleValue: [] },
                        { fieldName: 'email', simpleValue: [] },
                    ],
                },
                {
                    fields: [
                        { fieldName: 'address', simpleValue: [] },
                        { fieldName: 'phone', simpleValue: [] },
                    ],
                },
            ],
        } as unknown as Register;

        const formValues = {
            name: 'Bob',
            email: '<EMAIL>',
            address: '123 Main St',
            phone: '555-1234',
        };

        const updatedRecord = updateEntryRecord(record, formValues);

        expect(updatedRecord.sections).toBeDefined();

        expect(updatedRecord.sections![0].fields?.find((f) => f.fieldName === 'name')?.simpleValue).toEqual(['Bob']);
        expect(updatedRecord.sections![0].fields?.find((f) => f.fieldName === 'email')?.simpleValue).toEqual(['<EMAIL>']);
        expect(updatedRecord.sections![1].fields?.find((f) => f.fieldName === 'address')?.simpleValue).toEqual(['123 Main St']);
        expect(updatedRecord.sections![1].fields?.find((f) => f.fieldName === 'phone')?.simpleValue).toEqual(['555-1234']);
    });

    it('should not modify the original record object', () => {
        const record = {
            sections: [
                {
                    fields: [{ fieldName: 'name', simpleValue: [] }],
                },
            ],
        } as unknown as Register;

        const formValues = {
            name: 'Eve',
        };

        const originalRecordCopy = JSON.parse(JSON.stringify(record));
        updateEntryRecord(record, formValues);

        expect(record).toEqual(originalRecordCopy);
    });

    it('should skip formValues keys that are not in the record fields', () => {
        const record = {
            sections: [
                {
                    fields: [{ fieldName: 'name', simpleValue: [] }],
                },
            ],
        } as unknown as Register;

        const formValues = {
            name: 'Charlie',
            unknownField: 'Should be ignored',
        };

        const updatedRecord = updateEntryRecord(record, formValues);

        expect(updatedRecord.sections).toBeDefined();
        expect(updatedRecord.sections![0].fields?.find((f) => f.fieldName === 'name')?.simpleValue).toEqual(['Charlie']);
        expect(updatedRecord).not.toHaveProperty('unknownField');

        expect(updatedRecord).toBeDefined();
    });
});


describe('getFieldsByColumnType', () => {
    const createMockField = (overrides: Partial<FieldRest> = {}): FieldRest => ({
        id: 1,
        label: 'Test Field',
        columnName: 'col_test',
        columnType: ColumnType.SINGLELINE_TEXT,
        archived: false,
        ...overrides,
    });

    const createMockSection = (overrides: Partial<SectionRest> = {}): SectionRest => ({
        id: 1,
        label: 'Test Section',
        fields: [],
        ...overrides,
    });

    const createMockRegister = (overrides: Partial<TableMetadataRest> = {}): TableMetadataRest => ({
        id: 1,
        label: 'Test Register',
        sections: [],
        ...overrides,
    });

    describe('basic functionality', () => {
        it('returns empty array when register is null', () => {
            expect(getFieldsByColumnType(null as any, ColumnType.SINGLELINE_TEXT)).toEqual([]);
        });

        it('returns empty array when register is undefined', () => {
            expect(getFieldsByColumnType(undefined as any, ColumnType.SINGLELINE_TEXT)).toEqual([]);
        });

        it('returns empty array when register has no sections', () => {
            const register = createMockRegister();
            expect(getFieldsByColumnType(register, ColumnType.SINGLELINE_TEXT)).toEqual([]);
        });

        it('returns empty array when sections have no fields', () => {
            const register = createMockRegister({
                sections: [createMockSection()],
            });
            expect(getFieldsByColumnType(register, ColumnType.SINGLELINE_TEXT)).toEqual([]);
        });

        it('returns fields matching the specified column type', () => {
            const textField = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_text' });
            const numericField = createMockField({ columnType: ColumnType.NUMERIC, columnName: 'col_numeric' });
            const emailField = createMockField({ columnType: ColumnType.EMAIL, columnName: 'col_email' });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [textField, numericField, emailField],
                    }),
                ],
            });

            const result = getFieldsByColumnType(register, ColumnType.SINGLELINE_TEXT);
            expect(result).toEqual([textField]);
        });

        it('returns fields from multiple sections matching the column type', () => {
            const textField1 = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_text1' });
            const textField2 = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_text2' });
            const numericField = createMockField({ columnType: ColumnType.NUMERIC, columnName: 'col_numeric' });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        label: 'Section 1',
                        fields: [textField1, numericField],
                    }),
                    createMockSection({
                        label: 'Section 2',
                        fields: [textField2],
                    }),
                ],
            });

            const result = getFieldsByColumnType(register, ColumnType.SINGLELINE_TEXT);
            expect(result).toEqual([textField1, textField2]);
        });

        it('returns empty array when no fields match the column type', () => {
            const numericField = createMockField({ columnType: ColumnType.NUMERIC, columnName: 'col_numeric' });
            const emailField = createMockField({ columnType: ColumnType.EMAIL, columnName: 'col_email' });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [numericField, emailField],
                    }),
                ],
            });

            const result = getFieldsByColumnType(register, ColumnType.SINGLELINE_TEXT);
            expect(result).toEqual([]);
        });
    });

    describe('core section filtering', () => {
        it('excludes core section fields by default', () => {
            const coreField = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_core' });
            const regularField = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_regular' });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        label: CORE_SECTION_LABEL,
                        fields: [coreField],
                    }),
                    createMockSection({
                        label: 'Regular Section',
                        fields: [regularField],
                    }),
                ],
            });

            const result = getFieldsByColumnType(register, ColumnType.SINGLELINE_TEXT);
            expect(result).toEqual([regularField]);
        });

        it('includes core section fields when includeCoreSection is true', () => {
            const coreField = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_core' });
            const regularField = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_regular' });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        label: CORE_SECTION_LABEL,
                        fields: [coreField],
                    }),
                    createMockSection({
                        label: 'Regular Section',
                        fields: [regularField],
                    }),
                ],
            });

            const result = getFieldsByColumnType(register, ColumnType.SINGLELINE_TEXT, true);
            expect(result).toEqual([coreField, regularField]);
        });

        it('only returns core section fields when only core section exists and includeCoreSection is true', () => {
            const coreField = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_core' });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        label: CORE_SECTION_LABEL,
                        fields: [coreField],
                    }),
                ],
            });

            const result = getFieldsByColumnType(register, ColumnType.SINGLELINE_TEXT, true);
            expect(result).toEqual([coreField]);
        });
    });

    describe('auxiliary field filtering', () => {
        it('excludes auxiliary fields by default', () => {
            const regularField = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_regular' });
            const auxiliaryField = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_123_aux0' });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        label: 'Regular Section',
                        fields: [regularField, auxiliaryField],
                    }),
                ],
            });

            const result = getFieldsByColumnType(register, ColumnType.SINGLELINE_TEXT);
            expect(result).toEqual([regularField]);
        });

        it('includes auxiliary fields when includeAuxiliary is true', () => {
            const regularField = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_regular' });
            const auxiliaryField = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_123_aux0' });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        label: 'Regular Section',
                        fields: [regularField, auxiliaryField],
                    }),
                ],
            });

            const result = getFieldsByColumnType(register, ColumnType.SINGLELINE_TEXT, false, true);
            expect(result).toEqual([regularField, auxiliaryField]);
        });
    });

    describe('archived field filtering', () => {
        it('excludes archived fields by default', () => {
            const activeField = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_active', archived: false });
            const archivedField = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_archived', archived: true });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        label: 'Regular Section',
                        fields: [activeField, archivedField],
                    }),
                ],
            });

            const result = getFieldsByColumnType(register, ColumnType.SINGLELINE_TEXT);
            expect(result).toEqual([activeField]);
        });

        it('includes archived fields when includeArchivedFields is true', () => {
            const activeField = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_active', archived: false });
            const archivedField = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_archived', archived: true });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        label: 'Regular Section',
                        fields: [activeField, archivedField],
                    }),
                ],
            });

            const result = getFieldsByColumnType(register, ColumnType.SINGLELINE_TEXT, false, false, true);
            expect(result).toEqual([activeField, archivedField]);
        });

        it('handles fields with undefined archived property', () => {
            const fieldWithUndefinedArchived = createMockField({
                columnType: ColumnType.SINGLELINE_TEXT,
                columnName: 'col_undefined',
                archived: undefined,
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        label: 'Regular Section',
                        fields: [fieldWithUndefinedArchived],
                    }),
                ],
            });

            const result = getFieldsByColumnType(register, ColumnType.SINGLELINE_TEXT);
            expect(result).toEqual([fieldWithUndefinedArchived]);
        });
    });

    describe('combined filtering options', () => {
        it('applies all filters when includeCoreSection=true, includeAuxiliary=true, includeArchivedFields=true', () => {
            const coreActiveField = createMockField({
                columnType: ColumnType.SINGLELINE_TEXT,
                columnName: 'col_core_active',
                archived: false,
            });
            const coreArchivedField = createMockField({
                columnType: ColumnType.SINGLELINE_TEXT,
                columnName: 'col_core_archived',
                archived: true,
            });
            const coreAuxiliaryField = createMockField({
                columnType: ColumnType.SINGLELINE_TEXT,
                columnName: 'col_456_aux1',
                archived: false,
            });
            const regularField = createMockField({
                columnType: ColumnType.SINGLELINE_TEXT,
                columnName: 'col_regular',
                archived: false,
            });
            const auxiliaryField = createMockField({
                columnType: ColumnType.SINGLELINE_TEXT,
                columnName: 'col_123_aux0',
                archived: false,
            });
            const archivedField = createMockField({
                columnType: ColumnType.SINGLELINE_TEXT,
                columnName: 'col_archived',
                archived: true,
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        label: CORE_SECTION_LABEL,
                        fields: [coreActiveField, coreArchivedField, coreAuxiliaryField],
                    }),
                    createMockSection({
                        label: 'Regular Section',
                        fields: [regularField, auxiliaryField, archivedField],
                    }),
                ],
            });

            const result = getFieldsByColumnType(register, ColumnType.SINGLELINE_TEXT, true, true, true);
            expect(result).toEqual([coreActiveField, coreArchivedField, coreAuxiliaryField, regularField, auxiliaryField, archivedField]);
        });

        it('applies core and auxiliary filters but excludes archived fields', () => {
            const coreActiveField = createMockField({
                columnType: ColumnType.SINGLELINE_TEXT,
                columnName: 'col_core_active',
                archived: false,
            });
            const coreArchivedField = createMockField({
                columnType: ColumnType.SINGLELINE_TEXT,
                columnName: 'col_core_archived',
                archived: true,
            });
            const auxiliaryField = createMockField({
                columnType: ColumnType.SINGLELINE_TEXT,
                columnName: 'col_123_aux0',
                archived: false,
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        label: CORE_SECTION_LABEL,
                        fields: [coreActiveField, coreArchivedField],
                    }),
                    createMockSection({
                        label: 'Regular Section',
                        fields: [auxiliaryField],
                    }),
                ],
            });

            const result = getFieldsByColumnType(register, ColumnType.SINGLELINE_TEXT, true, true, false);
            expect(result).toEqual([coreActiveField, auxiliaryField]);
        });
    });

    describe('edge cases', () => {
        it('handles sections with undefined fields array', () => {
            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: undefined,
                    }),
                ],
            });

            const result = getFieldsByColumnType(register, ColumnType.SINGLELINE_TEXT);
            expect(result).toEqual([]);
        });

        it('handles register with undefined sections array', () => {
            const register = createMockRegister({
                sections: undefined,
            });

            const result = getFieldsByColumnType(register, ColumnType.SINGLELINE_TEXT);
            expect(result).toEqual([]);
        });

        it('handles fields with null columnType', () => {
            const fieldWithNullColumnType = createMockField({
                columnType: null as any,
                columnName: 'col_null_type',
            });
            const validField = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_valid' });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [fieldWithNullColumnType, validField],
                    }),
                ],
            });

            const result = getFieldsByColumnType(register, ColumnType.SINGLELINE_TEXT);
            expect(result).toEqual([validField]);
        });

        it('handles fields with undefined columnType', () => {
            const fieldWithUndefinedColumnType = createMockField({
                columnType: undefined as any,
                columnName: 'col_undefined_type',
            });
            const validField = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_valid' });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [fieldWithUndefinedColumnType, validField],
                    }),
                ],
            });

            const result = getFieldsByColumnType(register, ColumnType.SINGLELINE_TEXT);
            expect(result).toEqual([validField]);
        });
    });

    describe('different column types', () => {
        it('correctly filters by EMAIL column type', () => {
            const emailField = createMockField({ columnType: ColumnType.EMAIL, columnName: 'col_email' });
            const textField = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_text' });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [emailField, textField],
                    }),
                ],
            });

            const result = getFieldsByColumnType(register, ColumnType.EMAIL);
            expect(result).toEqual([emailField]);
        });

        it('correctly filters by NUMERIC column type', () => {
            const numericField = createMockField({ columnType: ColumnType.NUMERIC, columnName: 'col_numeric' });
            const textField = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_text' });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [numericField, textField],
                    }),
                ],
            });

            const result = getFieldsByColumnType(register, ColumnType.NUMERIC);
            expect(result).toEqual([numericField]);
        });

        it('correctly filters by BOOLEAN column type', () => {
            const booleanField = createMockField({ columnType: ColumnType.BOOLEAN, columnName: 'col_boolean' });
            const textField = createMockField({ columnType: ColumnType.SINGLELINE_TEXT, columnName: 'col_text' });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [booleanField, textField],
                    }),
                ],
            });

            const result = getFieldsByColumnType(register, ColumnType.BOOLEAN);
            expect(result).toEqual([booleanField]);
        });
    });

    describe('registerHasUserFieldWithMessaging', () => {
        it('returns false when register is null', () => {
            const result = registerHasUserFieldWithMessaging(null as any);
            expect(result).toBe(false);
        });

        it('returns false when register is undefined', () => {
            const result = registerHasUserFieldWithMessaging(undefined);
            expect(result).toBe(false);
        });

        it('returns false when register has no USER fields', () => {
            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [createMockField({ columnType: ColumnType.SINGLELINE_TEXT }), createMockField({ columnType: ColumnType.EMAIL })],
                    }),
                ],
            });

            const result = registerHasUserFieldWithMessaging(register);
            expect(result).toBe(false);
        });

        it('returns false when USER fields exist but none have messaging enabled', () => {
            const userField1 = createMockField({
                columnType: ColumnType.USER,
                constraintProperties: {
                    messaging: { value: 'false' },
                },
            });
            const userField2 = createMockField({
                columnType: ColumnType.USER,
                constraintProperties: {},
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [userField1, userField2],
                    }),
                ],
            });

            const result = registerHasUserFieldWithMessaging(register);
            expect(result).toBe(false);
        });

        it('returns true when at least one USER field has messaging enabled', () => {
            const userFieldWithMessaging = createMockField({
                columnType: ColumnType.USER,
                constraintProperties: {
                    messaging: { value: 'true' },
                },
            });
            const userFieldWithoutMessaging = createMockField({
                columnType: ColumnType.USER,
                constraintProperties: {
                    messaging: { value: 'false' },
                },
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [userFieldWithMessaging, userFieldWithoutMessaging],
                    }),
                ],
            });

            const result = registerHasUserFieldWithMessaging(register);
            expect(result).toBe(true);
        });

        it('returns true when multiple USER fields have messaging enabled', () => {
            const userField1 = createMockField({
                columnType: ColumnType.USER,
                constraintProperties: {
                    messaging: { value: 'true' },
                },
            });
            const userField2 = createMockField({
                columnType: ColumnType.USER,
                constraintProperties: {
                    messaging: { value: 'true' },
                },
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [userField1, userField2],
                    }),
                ],
            });

            const result = registerHasUserFieldWithMessaging(register);
            expect(result).toBe(true);
        });

        it('returns false when USER field has undefined constraintProperties', () => {
            const userField = createMockField({
                columnType: ColumnType.USER,
                constraintProperties: undefined,
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [userField],
                    }),
                ],
            });

            const result = registerHasUserFieldWithMessaging(register);
            expect(result).toBe(false);
        });

        it('returns false when USER field has null constraintProperties', () => {
            const userField = createMockField({
                columnType: ColumnType.USER,
                constraintProperties: undefined,
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [userField],
                    }),
                ],
            });

            const result = registerHasUserFieldWithMessaging(register);
            expect(result).toBe(false);
        });

        it('handles mixed field types and only considers USER fields', () => {
            const nonUserField = createMockField({
                columnType: ColumnType.SINGLELINE_TEXT,
                constraintProperties: {
                    messaging: { value: 'true' },
                },
            });
            const userFieldWithMessaging = createMockField({
                columnType: ColumnType.USER,
                constraintProperties: {
                    messaging: { value: 'true' },
                },
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [nonUserField, userFieldWithMessaging],
                    }),
                ],
            });

            const result = registerHasUserFieldWithMessaging(register);
            expect(result).toBe(true);
        });

        it('returns false when register has empty sections', () => {
            const register = createMockRegister({
                sections: [],
            });

            const result = registerHasUserFieldWithMessaging(register);
            expect(result).toBe(false);
        });
    });

    describe('registerHasAttachmentField', () => {
        it('returns false when register is null', () => {
            const result = registerHasAttachmentField(null as any);
            expect(result).toBe(false);
        });

        it('returns false when register is undefined', () => {
            const result = registerHasAttachmentField(undefined);
            expect(result).toBe(false);
        });

        it('returns false when register has no ATTACHMENT or IMAGES fields', () => {
            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [
                            createMockField({ columnType: ColumnType.SINGLELINE_TEXT }),
                            createMockField({ columnType: ColumnType.EMAIL }),
                            createMockField({ columnType: ColumnType.USER }),
                        ],
                    }),
                ],
            });

            const result = registerHasAttachmentField(register);
            expect(result).toBe(false);
        });

        it('returns true when register has ATTACHMENT fields', () => {
            const attachmentField = createMockField({
                columnType: ColumnType.ATTACHMENT,
                columnName: 'col_attachment',
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [createMockField({ columnType: ColumnType.SINGLELINE_TEXT }), attachmentField],
                    }),
                ],
            });

            const result = registerHasAttachmentField(register);
            expect(result).toBe(true);
        });

        it('returns true when register has IMAGES fields', () => {
            const imagesField = createMockField({
                columnType: ColumnType.IMAGES,
                columnName: 'col_images',
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [createMockField({ columnType: ColumnType.SINGLELINE_TEXT }), imagesField],
                    }),
                ],
            });

            const result = registerHasAttachmentField(register);
            expect(result).toBe(true);
        });

        it('returns true when register has both ATTACHMENT and IMAGES fields', () => {
            const attachmentField = createMockField({
                columnType: ColumnType.ATTACHMENT,
                columnName: 'col_attachment',
            });
            const imagesField = createMockField({
                columnType: ColumnType.IMAGES,
                columnName: 'col_images',
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [attachmentField, imagesField],
                    }),
                ],
            });

            const result = registerHasAttachmentField(register);
            expect(result).toBe(true);
        });

        it('returns true when attachment fields are in multiple sections', () => {
            const attachmentField1 = createMockField({
                columnType: ColumnType.ATTACHMENT,
                columnName: 'col_attachment1',
            });
            const imagesField2 = createMockField({
                columnType: ColumnType.IMAGES,
                columnName: 'col_images2',
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        label: 'Section 1',
                        fields: [attachmentField1],
                    }),
                    createMockSection({
                        label: 'Section 2',
                        fields: [imagesField2],
                    }),
                ],
            });

            const result = registerHasAttachmentField(register);
            expect(result).toBe(true);
        });

        it('returns false when register has empty sections', () => {
            const register = createMockRegister({
                sections: [],
            });

            const result = registerHasAttachmentField(register);
            expect(result).toBe(false);
        });

        it('returns false when sections have no fields', () => {
            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [],
                    }),
                ],
            });

            const result = registerHasAttachmentField(register);
            expect(result).toBe(false);
        });

        it('handles mixed field types and only considers ATTACHMENT and IMAGES fields', () => {
            const textField = createMockField({
                columnType: ColumnType.SINGLELINE_TEXT,
                columnName: 'col_text',
            });
            const userField = createMockField({
                columnType: ColumnType.USER,
                columnName: 'col_user',
            });
            const attachmentField = createMockField({
                columnType: ColumnType.ATTACHMENT,
                columnName: 'col_attachment',
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [textField, userField, attachmentField],
                    }),
                ],
            });

            const result = registerHasAttachmentField(register);
            expect(result).toBe(true);
        });
    });

    describe('registerHasFormulaField', () => {
        it('returns false when register is null', () => {
            const result = registerHasFormulaField(null as any);
            expect(result).toBe(false);
        });

        it('returns false when register is undefined', () => {
            const result = registerHasFormulaField(undefined);
            expect(result).toBe(false);
        });

        it('returns false when register has no formula fields', () => {
            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [
                            createMockField({ columnType: ColumnType.SINGLELINE_TEXT }),
                            createMockField({ columnType: ColumnType.EMAIL }),
                            createMockField({ columnType: ColumnType.USER }),
                            createMockField({ columnType: ColumnType.ATTACHMENT }),
                        ],
                    }),
                ],
            });

            const result = registerHasFormulaField(register);
            expect(result).toBe(false);
        });

        it('returns true when register has SIMPLE_FORMULA fields', () => {
            const simpleFormulaField = createMockField({
                columnType: ColumnType.SIMPLE_FORMULA,
                columnName: 'col_simple_formula',
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [createMockField({ columnType: ColumnType.SINGLELINE_TEXT }), simpleFormulaField],
                    }),
                ],
            });

            const result = registerHasFormulaField(register);
            expect(result).toBe(true);
        });

        it('returns true when register has DATETIME_FORMULA fields', () => {
            const dateTimeFormulaField = createMockField({
                columnType: ColumnType.DATETIME_FORMULA,
                columnName: 'col_datetime_formula',
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [createMockField({ columnType: ColumnType.SINGLELINE_TEXT }), dateTimeFormulaField],
                    }),
                ],
            });

            const result = registerHasFormulaField(register);
            expect(result).toBe(true);
        });

        it('returns true when register has STRING_FORMULA fields', () => {
            const stringFormulaField = createMockField({
                columnType: ColumnType.STRING_FORMULA,
                columnName: 'col_string_formula',
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [createMockField({ columnType: ColumnType.SINGLELINE_TEXT }), stringFormulaField],
                    }),
                ],
            });

            const result = registerHasFormulaField(register);
            expect(result).toBe(true);
        });

        it('returns true when register has COMPLEX_FORMULA fields', () => {
            const complexFormulaField = createMockField({
                columnType: ColumnType.COMPLEX_FORMULA,
                columnName: 'col_complex_formula',
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [createMockField({ columnType: ColumnType.SINGLELINE_TEXT }), complexFormulaField],
                    }),
                ],
            });

            const result = registerHasFormulaField(register);
            expect(result).toBe(true);
        });

        it('returns true when register has multiple types of formula fields', () => {
            const simpleFormulaField = createMockField({
                columnType: ColumnType.SIMPLE_FORMULA,
                columnName: 'col_simple_formula',
            });
            const dateTimeFormulaField = createMockField({
                columnType: ColumnType.DATETIME_FORMULA,
                columnName: 'col_datetime_formula',
            });
            const stringFormulaField = createMockField({
                columnType: ColumnType.STRING_FORMULA,
                columnName: 'col_string_formula',
            });
            const complexFormulaField = createMockField({
                columnType: ColumnType.COMPLEX_FORMULA,
                columnName: 'col_complex_formula',
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [simpleFormulaField, dateTimeFormulaField, stringFormulaField, complexFormulaField],
                    }),
                ],
            });

            const result = registerHasFormulaField(register);
            expect(result).toBe(true);
        });

        it('returns true when formula fields are in multiple sections', () => {
            const simpleFormulaField = createMockField({
                columnType: ColumnType.SIMPLE_FORMULA,
                columnName: 'col_simple_formula',
            });
            const dateTimeFormulaField = createMockField({
                columnType: ColumnType.DATETIME_FORMULA,
                columnName: 'col_datetime_formula',
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        label: 'Section 1',
                        fields: [simpleFormulaField],
                    }),
                    createMockSection({
                        label: 'Section 2',
                        fields: [dateTimeFormulaField],
                    }),
                ],
            });

            const result = registerHasFormulaField(register);
            expect(result).toBe(true);
        });

        it('returns false when register has empty sections', () => {
            const register = createMockRegister({
                sections: [],
            });

            const result = registerHasFormulaField(register);
            expect(result).toBe(false);
        });

        it('returns false when sections have no fields', () => {
            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [],
                    }),
                ],
            });

            const result = registerHasFormulaField(register);
            expect(result).toBe(false);
        });

        it('handles mixed field types and only considers formula fields', () => {
            const textField = createMockField({
                columnType: ColumnType.SINGLELINE_TEXT,
                columnName: 'col_text',
            });
            const userField = createMockField({
                columnType: ColumnType.USER,
                columnName: 'col_user',
            });
            const attachmentField = createMockField({
                columnType: ColumnType.ATTACHMENT,
                columnName: 'col_attachment',
            });
            const formulaField = createMockField({
                columnType: ColumnType.SIMPLE_FORMULA,
                columnName: 'col_formula',
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [textField, userField, attachmentField, formulaField],
                    }),
                ],
            });

            const result = registerHasFormulaField(register);
            expect(result).toBe(true);
        });

        it('returns true for each individual formula type separately', () => {
            // Test SIMPLE_FORMULA
            const simpleFormulaRegister = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [createMockField({ columnType: ColumnType.SIMPLE_FORMULA })],
                    }),
                ],
            });
            expect(registerHasFormulaField(simpleFormulaRegister)).toBe(true);

            // Test DATETIME_FORMULA
            const dateTimeFormulaRegister = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [createMockField({ columnType: ColumnType.DATETIME_FORMULA })],
                    }),
                ],
            });
            expect(registerHasFormulaField(dateTimeFormulaRegister)).toBe(true);

            // Test STRING_FORMULA
            const stringFormulaRegister = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [createMockField({ columnType: ColumnType.STRING_FORMULA })],
                    }),
                ],
            });
            expect(registerHasFormulaField(stringFormulaRegister)).toBe(true);

            // Test COMPLEX_FORMULA
            const complexFormulaRegister = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [createMockField({ columnType: ColumnType.COMPLEX_FORMULA })],
                    }),
                ],
            });
            expect(registerHasFormulaField(complexFormulaRegister)).toBe(true);
        });
    });

    describe('registerHasDueDateField', () => {
        it('returns false when register is null', () => {
            const result = registerHasDueDateField(null as any);
            expect(result).toBe(false);
        });

        it('returns false when register is undefined', () => {
            const result = registerHasDueDateField(undefined);
            expect(result).toBe(false);
        });

        it('returns false when register has no DUE_DATE fields', () => {
            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [
                            createMockField({ columnType: ColumnType.SINGLELINE_TEXT }),
                            createMockField({ columnType: ColumnType.EMAIL }),
                            createMockField({ columnType: ColumnType.USER }),
                            createMockField({ columnType: ColumnType.ATTACHMENT }),
                            createMockField({ columnType: ColumnType.SIMPLE_FORMULA }),
                        ],
                    }),
                ],
            });

            const result = registerHasDueDateField(register);
            expect(result).toBe(false);
        });

        it('returns true when register has DUE_DATE fields', () => {
            const dueDateField = createMockField({
                columnType: ColumnType.DUE_DATE,
                columnName: 'col_due_date',
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [createMockField({ columnType: ColumnType.SINGLELINE_TEXT }), dueDateField],
                    }),
                ],
            });

            const result = registerHasDueDateField(register);
            expect(result).toBe(true);
        });

        it('returns true when register has multiple DUE_DATE fields', () => {
            const dueDateField1 = createMockField({
                columnType: ColumnType.DUE_DATE,
                columnName: 'col_due_date_1',
            });
            const dueDateField2 = createMockField({
                columnType: ColumnType.DUE_DATE,
                columnName: 'col_due_date_2',
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [dueDateField1, dueDateField2],
                    }),
                ],
            });

            const result = registerHasDueDateField(register);
            expect(result).toBe(true);
        });

        it('returns true when DUE_DATE fields are in multiple sections', () => {
            const dueDateField1 = createMockField({
                columnType: ColumnType.DUE_DATE,
                columnName: 'col_due_date_1',
            });
            const dueDateField2 = createMockField({
                columnType: ColumnType.DUE_DATE,
                columnName: 'col_due_date_2',
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        label: 'Section 1',
                        fields: [dueDateField1],
                    }),
                    createMockSection({
                        label: 'Section 2',
                        fields: [dueDateField2],
                    }),
                ],
            });

            const result = registerHasDueDateField(register);
            expect(result).toBe(true);
        });

        it('returns false when register has empty sections', () => {
            const register = createMockRegister({
                sections: [],
            });

            const result = registerHasDueDateField(register);
            expect(result).toBe(false);
        });

        it('returns false when sections have no fields', () => {
            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [],
                    }),
                ],
            });

            const result = registerHasDueDateField(register);
            expect(result).toBe(false);
        });

        it('handles mixed field types and only considers DUE_DATE fields', () => {
            const textField = createMockField({
                columnType: ColumnType.SINGLELINE_TEXT,
                columnName: 'col_text',
            });
            const userField = createMockField({
                columnType: ColumnType.USER,
                columnName: 'col_user',
            });
            const attachmentField = createMockField({
                columnType: ColumnType.ATTACHMENT,
                columnName: 'col_attachment',
            });
            const formulaField = createMockField({
                columnType: ColumnType.SIMPLE_FORMULA,
                columnName: 'col_formula',
            });
            const dueDateField = createMockField({
                columnType: ColumnType.DUE_DATE,
                columnName: 'col_due_date',
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [textField, userField, attachmentField, formulaField, dueDateField],
                    }),
                ],
            });

            const result = registerHasDueDateField(register);
            expect(result).toBe(true);
        });

        it('returns false when register only has other date-related fields but no DUE_DATE', () => {
            const dateField = createMockField({
                columnType: ColumnType.DATE,
                columnName: 'col_date',
            });
            const timestampField = createMockField({
                columnType: ColumnType.TIMESTAMP,
                columnName: 'col_timestamp',
            });
            const dateTimeFormulaField = createMockField({
                columnType: ColumnType.DATETIME_FORMULA,
                columnName: 'col_datetime_formula',
            });

            const register = createMockRegister({
                sections: [
                    createMockSection({
                        fields: [dateField, timestampField, dateTimeFormulaField],
                    }),
                ],
            });

            const result = registerHasDueDateField(register);
            expect(result).toBe(false);
        });
    });

    describe('getEntryBreadcrumbs', () => {
        beforeEach(() => {
            mockedGeneratePath.mockClear();
        });

        it('returns breadcrumbs with application and register labels', () => {
            const register = createMockRegister({
                applicationName: 'Test Application',
                applicationId: 123,
                label: 'Test Register',
                tableName: 'test_table',
            });

            mockedGeneratePath.mockReturnValueOnce('/app/123/registers').mockReturnValueOnce('/app/123/registers/test_table/entries');

            const result = getEntryBreadcrumbs(register);

            expect(result).toHaveLength(2);
            expect(result[0]).toEqual({
                label: 'Test Application',
                pathname: '/app/123/registers',
            });
            expect(result[1]).toEqual({
                label: 'Test Register',
                pathname: '/app/123/registers/test_table/entries',
            });
        });

        it('calls generatePath with correct parameters for application breadcrumb', () => {
            const register = createMockRegister({
                applicationName: 'My App',
                applicationId: 456,
                label: 'My Register',
                tableName: 'my_table',
            });

            mockedGeneratePath.mockReturnValueOnce('/app/456/registers').mockReturnValueOnce('/app/456/registers/my_table/entries');

            getEntryBreadcrumbs(register);

            expect(mockedGeneratePath).toHaveBeenCalledWith(RegisterPath.REGISTER_LIST, {
                appId: '456',
            });
        });

        it('calls generatePath with correct parameters for register breadcrumb', () => {
            const register = createMockRegister({
                applicationName: 'Another App',
                applicationId: 789,
                label: 'Another Register',
                tableName: 'another_table',
            });

            mockedGeneratePath.mockReturnValueOnce('/app/789/registers').mockReturnValueOnce('/app/789/registers/another_table/entries');

            getEntryBreadcrumbs(register);

            expect(mockedGeneratePath).toHaveBeenCalledWith(RegisterPath.REGISTER_LIST_REGISTER, {
                appId: '789',
                tableName: 'another_table',
            });
        });

        it('handles numeric application ID correctly', () => {
            const register = createMockRegister({
                applicationName: 'Numeric App',
                applicationId: 999,
                label: 'Numeric Register',
                tableName: 'numeric_table',
            });

            mockedGeneratePath.mockReturnValueOnce('/app/999/registers').mockReturnValueOnce('/app/999/registers/numeric_table/entries');

            getEntryBreadcrumbs(register);

            expect(mockedGeneratePath).toHaveBeenNthCalledWith(1, RegisterPath.REGISTER_LIST, {
                appId: '999',
            });
            expect(mockedGeneratePath).toHaveBeenNthCalledWith(2, RegisterPath.REGISTER_LIST_REGISTER, {
                appId: '999',
                tableName: 'numeric_table',
            });
        });

        it('handles special characters in application and register names', () => {
            const register = createMockRegister({
                applicationName: 'Special & Characters!',
                applicationId: 111,
                label: 'Register with "quotes" and symbols',
                tableName: 'special_table',
            });

            mockedGeneratePath.mockReturnValueOnce('/app/111/registers').mockReturnValueOnce('/app/111/registers/special_table/entries');

            const result = getEntryBreadcrumbs(register);

            expect(result[0].label).toBe('Special & Characters!');
            expect(result[1].label).toBe('Register with "quotes" and symbols');
        });

        it('handles empty strings in application and register names', () => {
            const register = createMockRegister({
                applicationName: '',
                applicationId: 222,
                label: '',
                tableName: 'empty_table',
            });

            mockedGeneratePath.mockReturnValueOnce('/app/222/registers').mockReturnValueOnce('/app/222/registers/empty_table/entries');

            const result = getEntryBreadcrumbs(register);

            expect(result[0].label).toBe('');
            expect(result[1].label).toBe('');
        });

        it('handles table names with special characters', () => {
            const register = createMockRegister({
                applicationName: 'Test App',
                applicationId: 333,
                label: 'Test Register',
                tableName: 'table_with-special.chars',
            });

            mockedGeneratePath.mockReturnValueOnce('/app/333/registers').mockReturnValueOnce('/app/333/registers/table_with-special.chars/entries');

            getEntryBreadcrumbs(register);

            expect(mockedGeneratePath).toHaveBeenCalledWith(RegisterPath.REGISTER_LIST_REGISTER, {
                appId: '333',
                tableName: 'table_with-special.chars',
            });
        });

        it('calls generatePath exactly twice', () => {
            const register = createMockRegister({
                applicationName: 'Call Count Test',
                applicationId: 444,
                label: 'Call Count Register',
                tableName: 'call_count_table',
            });

            mockedGeneratePath.mockReturnValueOnce('/app/444/registers').mockReturnValueOnce('/app/444/registers/call_count_table/entries');

            getEntryBreadcrumbs(register);

            expect(mockedGeneratePath).toHaveBeenCalledTimes(2);
        });

        it('preserves the order of breadcrumbs (application first, then register)', () => {
            const register = createMockRegister({
                applicationName: 'Order Test App',
                applicationId: 555,
                label: 'Order Test Register',
                tableName: 'order_test_table',
            });

            mockedGeneratePath.mockReturnValueOnce('/app/555/registers').mockReturnValueOnce('/app/555/registers/order_test_table/entries');

            const result = getEntryBreadcrumbs(register);

            // First breadcrumb should be application
            expect(result[0].label).toBe('Order Test App');
            expect(result[0].pathname).toBe('/app/555/registers');

            // Second breadcrumb should be register
            expect(result[1].label).toBe('Order Test Register');
            expect(result[1].pathname).toBe('/app/555/registers/order_test_table/entries');
        });

        it('returns breadcrumbs with the exact structure expected by the UI', () => {
            const register = createMockRegister({
                applicationName: 'Structure Test App',
                applicationId: 666,
                label: 'Structure Test Register',
                tableName: 'structure_test_table',
            });

            mockedGeneratePath.mockReturnValueOnce('/app/666/registers').mockReturnValueOnce('/app/666/registers/structure_test_table/entries');

            const result = getEntryBreadcrumbs(register);

            // Verify structure of each breadcrumb
            expect(result[0]).toHaveProperty('label');
            expect(result[0]).toHaveProperty('pathname');
            expect(Object.keys(result[0])).toEqual(['label', 'pathname']);

            expect(result[1]).toHaveProperty('label');
            expect(result[1]).toHaveProperty('pathname');
            expect(Object.keys(result[1])).toEqual(['label', 'pathname']);
        });
    });
});

describe('shouldFormBeDirtyBasedOnDefaults', () => {
    beforeEach(() => {
        // Reset the mock before each test
        mockedGetListItems.mockReset();
    });

    const createMockRegisterWithFields = (fields: Partial<FieldRest>[]): RegisterRest => {
        return {
            id: 1,
            label: 'Test Register',
            sections: [
                {
                    id: 1,
                    label: 'Non-Core Section',
                    fields: fields.map((field, index) => ({
                        id: index + 1,
                        label: `Field ${index + 1}`,
                        columnName: `col_${index + 1}`,
                        columnType: ColumnType.SINGLELINE_TEXT,
                        archived: false,
                        ...field,
                    })),
                },
            ],
        } as RegisterRest;
    };

    const createMockEntryWithFields = (fieldsData: { fieldName: string; simpleValue?: string[] }[]): RegisterEntryRecord => {
        return {
            record: {
                id: 1,
                sections: [
                    {
                        label: 'Non-Core Section',
                        fields: fieldsData.map((fieldData) => ({
                            fieldName: fieldData.fieldName,
                            simpleValue: fieldData.simpleValue || [],
                        })),
                    },
                ],
            },
            completed: false,
        } as RegisterEntryRecord;
    };

    describe('returns false when required parameters are missing', () => {
        it('returns false when register is null', () => {
            const entry = createMockEntryWithFields([]);
            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(null, entry);
            expect(result).toBe(false);
        });

        it('returns false when entry is null', () => {
            const register = createMockRegisterWithFields([]);
            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(register, null);
            expect(result).toBe(false);
        });

        it('returns false when both register and entry are null', () => {
            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(null, null);
            expect(result).toBe(false);
        });
    });

    describe('returns false when entry is completed', () => {
        it('returns false when entry.completed is true', () => {
            const register = createMockRegisterWithFields([
                {
                    columnName: 'col_with_default',
                    constraintProperties: {
                        'default-value': { value: 'test default' },
                    },
                },
            ]);
            const entry = {
                ...createMockEntryWithFields([
                    { fieldName: 'col_with_default', simpleValue: ['test default'] },
                ]),
                completed: true,
            };

            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(register, entry);
            expect(result).toBe(false);
        });
    });

    describe('returns false when register has no default values', () => {
        it('returns false when no fields have default values', () => {
            const register = createMockRegisterWithFields([
                { columnName: 'col_1' },
                { columnName: 'col_2' },
            ]);
            const entry = createMockEntryWithFields([
                { fieldName: 'col_1', simpleValue: [] },
                { fieldName: 'col_2', simpleValue: [] },
            ]);

            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(register, entry);
            expect(result).toBe(false);
        });
    });

    describe('returns false when not all fields are at default values', () => {
        it('returns false when some fields have non-default values', () => {
            const register = createMockRegisterWithFields([
                {
                    columnName: 'col_with_default',
                    constraintProperties: {
                        'default-value': { value: 'default_value' },
                    },
                },
                { columnName: 'col_without_default' },
            ]);
            const entry = createMockEntryWithFields([
                { fieldName: 'col_with_default', simpleValue: ['default_value'] },
                { fieldName: 'col_without_default', simpleValue: ['user_entered_value'] },
            ]);

            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(register, entry);
            expect(result).toBe(false);
        });

        it('returns false when field with default value has been changed', () => {
            const register = createMockRegisterWithFields([
                {
                    columnName: 'col_with_default',
                    constraintProperties: {
                        'default-value': { value: 'default_value' },
                    },
                },
            ]);
            const entry = createMockEntryWithFields([
                { fieldName: 'col_with_default', simpleValue: ['changed_value'] },
            ]);

            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(register, entry);
            expect(result).toBe(false);
        });
    });

    describe('returns true when all conditions are met', () => {
        it('returns true when register has default values and all fields are at defaults', () => {
            const register = createMockRegisterWithFields([
                {
                    columnName: 'col_with_default',
                    constraintProperties: {
                        'default-value': { value: 'test_default' },
                    },
                },
                { columnName: 'col_without_default' },
            ]);
            const entry = createMockEntryWithFields([
                { fieldName: 'col_with_default', simpleValue: ['test_default'] },
                { fieldName: 'col_without_default', simpleValue: [] },
            ]);

            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(register, entry);
            expect(result).toBe(true);
        });

        it('returns true with multiple fields having default values', () => {
            const register = createMockRegisterWithFields([
                {
                    columnName: 'col_default_1',
                    constraintProperties: {
                        'default-value': { value: 'default_1' },
                    },
                },
                {
                    columnName: 'col_default_2',
                    constraintProperties: {
                        'default-value': { value: 'default_2' },
                    },
                },
            ]);
            const entry = createMockEntryWithFields([
                { fieldName: 'col_default_1', simpleValue: ['default_1'] },
                { fieldName: 'col_default_2', simpleValue: ['default_2'] },
            ]);

            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(register, entry);
            expect(result).toBe(true);
        });
    });

    describe('handles LIST fields with default options', () => {
        it('returns true when LIST field with default option is at default value', () => {
            // Mock getListItems to return list items with default option
            const mockListItems = [
                { value: 'option1', label: 'Option 1', default: false, visibility: true },
                { value: 'option2', label: 'Option 2', default: true, visibility: true },
                { value: 'option3', label: 'Option 3', default: false, visibility: true },
            ];
            
            // Use the properly mocked function
            mockedGetListItems.mockImplementation((field: any) => {
                if (field.columnName === 'col_list') {
                    return mockListItems;
                }
                return [];
            });

            const register = createMockRegisterWithFields([
                {
                    columnName: 'col_list',
                    columnType: ColumnType.LIST,
                    constraintProperties: {
                        'listValues': {
                            value: JSON.stringify(mockListItems),
                        },
                    },
                },
            ]);
            const entry = createMockEntryWithFields([
                { fieldName: 'col_list', simpleValue: ['option2'] },
            ]);

            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(register, entry);
            expect(result).toBe(true);
        });

        it('returns false when LIST field with default option has different value', () => {
            // Mock getListItems to return list items with default option
            const mockListItems = [
                { value: 'option1', label: 'Option 1', default: false, visibility: true },
                { value: 'option2', label: 'Option 2', default: true, visibility: true },
                { value: 'option3', label: 'Option 3', default: false, visibility: true },
            ];
            
            // Use the properly mocked function
            mockedGetListItems.mockImplementation((field: any) => {
                if (field.columnName === 'col_list') {
                    return mockListItems;
                }
                return [];
            });

            const register = createMockRegisterWithFields([
                {
                    columnName: 'col_list',
                    columnType: ColumnType.LIST,
                    constraintProperties: {
                        'listValues': {
                            value: JSON.stringify(mockListItems),
                        },
                    },
                },
            ]);
            const entry = createMockEntryWithFields([
                { fieldName: 'col_list', simpleValue: ['option1'] },
            ]);

            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(register, entry);
            expect(result).toBe(false);
        });
    });

    describe('handles DATE and TIMESTAMP fields with default-to-now', () => {
        it('returns true when DATE field has default-to-now constraint (skipped comparison)', () => {
            const register = createMockRegisterWithFields([
                {
                    columnName: 'col_date',
                    columnType: ColumnType.DATE,
                    constraintProperties: {
                        'default-to-now': { value: 'true' },
                    },
                },
                {
                    columnName: 'col_other',
                    constraintProperties: {
                        'default-value': { value: 'other_default' },
                    },
                },
            ]);
            const entry = createMockEntryWithFields([
                { fieldName: 'col_date', simpleValue: ['2023-01-01'] }, // Any value, should be skipped
                { fieldName: 'col_other', simpleValue: ['other_default'] },
            ]);

            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(register, entry);
            expect(result).toBe(true);
        });

        it('returns true when TIMESTAMP field has default-to-now constraint', () => {
            const register = createMockRegisterWithFields([
                {
                    columnName: 'col_timestamp',
                    columnType: ColumnType.TIMESTAMP,
                    constraintProperties: {
                        'default-to-now': { value: 'true' },
                    },
                },
            ]);
            const entry = createMockEntryWithFields([
                { fieldName: 'col_timestamp', simpleValue: ['2023-01-01T10:00:00'] },
            ]);

            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(register, entry);
            expect(result).toBe(true);
        });

        it('returns true when DUE_DATE field has default-to-now constraint', () => {
            const register = createMockRegisterWithFields([
                {
                    columnName: 'col_due_date',
                    columnType: ColumnType.DUE_DATE,
                    constraintProperties: {
                        'default-to-now': { value: 'true' },
                    },
                },
            ]);
            const entry = createMockEntryWithFields([
                { fieldName: 'col_due_date', simpleValue: ['2023-01-01'] },
            ]);

            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(register, entry);
            expect(result).toBe(true);
        });

        it('returns true when TIMESTAMP_WITH_TIMEZONE field has default-to-now constraint', () => {
            const register = createMockRegisterWithFields([
                {
                    columnName: 'col_timestamp_tz',
                    columnType: ColumnType.TIMESTAMP_WITH_TIMEZONE,
                    constraintProperties: {
                        'default-to-now': { value: 'true' },
                    },
                },
            ]);
            const entry = createMockEntryWithFields([
                { fieldName: 'col_timestamp_tz', simpleValue: ['2023-01-01T10:00:00+02:00'] },
            ]);

            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(register, entry);
            expect(result).toBe(true);
        });
    });

    describe('ignores core section fields', () => {
        it('returns true when only core section has defaults but they are ignored', () => {
            const register: RegisterRest = {
                id: 1,
                label: 'Test Register',
                sections: [
                    {
                        id: 1,
                        label: CORE_SECTION_LABEL,
                        fields: [
                            {
                                id: 1,
                                label: 'Core Field',
                                columnName: 'col_core',
                                columnType: ColumnType.SINGLELINE_TEXT,
                                archived: false,
                                constraintProperties: {
                                    'default-value': { value: 'core_default' },
                                },
                            },
                        ],
                    },
                    {
                        id: 2,
                        label: 'Regular Section',
                        fields: [
                            {
                                id: 2,
                                label: 'Regular Field',
                                columnName: 'col_regular',
                                columnType: ColumnType.SINGLELINE_TEXT,
                                archived: false,
                            },
                        ],
                    },
                ],
            } as RegisterRest;

            const entry = {
                record: {
                    id: 1,
                    sections: [
                        {
                            label: CORE_SECTION_LABEL,
                            fields: [
                                { fieldName: 'col_core', simpleValue: ['different_value'] },
                            ],
                        },
                        {
                            label: 'Regular Section',
                            fields: [
                                { fieldName: 'col_regular', simpleValue: [] },
                            ],
                        },
                    ],
                },
                completed: false,
            } as RegisterEntryRecord;

            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(register, entry);
            expect(result).toBe(false); // No defaults in non-core sections
        });

        it('returns true when non-core sections have defaults and core section is ignored', () => {
            const register: RegisterRest = {
                id: 1,
                label: 'Test Register',
                sections: [
                    {
                        id: 1,
                        label: CORE_SECTION_LABEL,
                        fields: [
                            {
                                id: 1,
                                label: 'Core Field',
                                columnName: 'col_core',
                                columnType: ColumnType.SINGLELINE_TEXT,
                                archived: false,
                                constraintProperties: {
                                    'default-value': { value: 'core_default' },
                                },
                            },
                        ],
                    },
                    {
                        id: 2,
                        label: 'Regular Section',
                        fields: [
                            {
                                id: 2,
                                label: 'Regular Field',
                                columnName: 'col_regular',
                                columnType: ColumnType.SINGLELINE_TEXT,
                                archived: false,
                                constraintProperties: {
                                    'default-value': { value: 'regular_default' },
                                },
                            },
                        ],
                    },
                ],
            } as RegisterRest;

            const entry = {
                record: {
                    id: 1,
                    sections: [
                        {
                            label: CORE_SECTION_LABEL,
                            fields: [
                                { fieldName: 'col_core', simpleValue: ['different_value'] },
                            ],
                        },
                        {
                            label: 'Regular Section',
                            fields: [
                                { fieldName: 'col_regular', simpleValue: ['regular_default'] },
                            ],
                        },
                    ],
                },
                completed: false,
            } as RegisterEntryRecord;

            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(register, entry);
            expect(result).toBe(true);
        });
    });

    describe('handles empty values correctly', () => {
        it('returns true when field without default has empty array and field with default has default value', () => {
            const register = createMockRegisterWithFields([
                {
                    columnName: 'col_with_default',
                    constraintProperties: {
                        'default-value': { value: 'default_val' },
                    },
                },
                { columnName: 'col_no_default' },
            ]);
            const entry = createMockEntryWithFields([
                { fieldName: 'col_with_default', simpleValue: ['default_val'] },
                { fieldName: 'col_no_default', simpleValue: [] },
            ]);

            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(register, entry);
            expect(result).toBe(true);
        });

        it('returns false when field with default has empty array instead of default value', () => {
            const register = createMockRegisterWithFields([
                {
                    columnName: 'col_with_default',
                    constraintProperties: {
                        'default-value': { value: 'default_val' },
                    },
                },
            ]);
            const entry = createMockEntryWithFields([
                { fieldName: 'col_with_default', simpleValue: [] },
            ]);

            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(register, entry);
            expect(result).toBe(false);
        });
    });

    describe('handles missing fields in entry', () => {
        it('returns true when entry is missing a field that exists in register with no default', () => {
            // When a field is missing from entry, it's treated as [] and compared with expected default []
            // This makes the comparison pass, so the result should be true if all other conditions are met
            const register = createMockRegisterWithFields([
                {
                    columnName: 'col_with_default',
                    constraintProperties: {
                        'default-value': { value: 'default_val' },
                    },
                },
                { columnName: 'col_missing_in_entry' }, // No default set
            ]);
            const entry = createMockEntryWithFields([
                { fieldName: 'col_with_default', simpleValue: ['default_val'] },
                // col_missing_in_entry is not included - treated as []
            ]);

            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(register, entry);
            expect(result).toBe(true); // Missing field with no default passes comparison
        });
    });

    describe('complex scenarios', () => {
        it('returns true with mixed field types all at default values', () => {
            // Mock getListItems for LIST field
            const mockListItems = [
                { value: 'opt1', label: 'Option 1', default: true, visibility: true },
                { value: 'opt2', label: 'Option 2', default: false, visibility: true },
            ];
            
            // Use the properly mocked function
            mockedGetListItems.mockImplementation((field: any) => {
                if (field.columnName === 'col_list_default') {
                    return mockListItems;
                }
                return [];
            });

            const register = createMockRegisterWithFields([
                {
                    columnName: 'col_text_default',
                    columnType: ColumnType.SINGLELINE_TEXT,
                    constraintProperties: {
                        'default-value': { value: 'text_default' },
                    },
                },
                {
                    columnName: 'col_list_default',
                    columnType: ColumnType.LIST,
                    constraintProperties: {
                        'listValues': {
                            value: JSON.stringify(mockListItems),
                        },
                    },
                },
                {
                    columnName: 'col_date_now',
                    columnType: ColumnType.DATE,
                    constraintProperties: {
                        'default-to-now': { value: 'true' },
                    },
                },
                { columnName: 'col_no_default' },
            ]);
            const entry = createMockEntryWithFields([
                { fieldName: 'col_text_default', simpleValue: ['text_default'] },
                { fieldName: 'col_list_default', simpleValue: ['opt1'] },
                { fieldName: 'col_date_now', simpleValue: ['2023-01-01'] }, // Skipped
                { fieldName: 'col_no_default', simpleValue: [] },
            ]);

            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(register, entry);
            expect(result).toBe(true);
        });

        it('returns false with mixed field types where one is not at default', () => {
            // Mock getListItems for LIST field
            const mockListItems = [
                { value: 'opt1', label: 'Option 1', default: true, visibility: true },
                { value: 'opt2', label: 'Option 2', default: false, visibility: true },
            ];
            
            // Use the properly mocked function
            mockedGetListItems.mockImplementation((field: any) => {
                if (field.columnName === 'col_list_default') {
                    return mockListItems;
                }
                return [];
            });

            const register = createMockRegisterWithFields([
                {
                    columnName: 'col_text_default',
                    columnType: ColumnType.SINGLELINE_TEXT,
                    constraintProperties: {
                        'default-value': { value: 'text_default' },
                    },
                },
                {
                    columnName: 'col_list_default',
                    columnType: ColumnType.LIST,
                    constraintProperties: {
                        'listValues': {
                            value: JSON.stringify(mockListItems),
                        },
                    },
                },
            ]);
            const entry = createMockEntryWithFields([
                { fieldName: 'col_text_default', simpleValue: ['text_default'] },
                { fieldName: 'col_list_default', simpleValue: ['opt2'] }, // Not default
            ]);

            const result = registerUtils.shouldFormBeDirtyBasedOnDefaults(register, entry);
            expect(result).toBe(false);
        });
    });
});
