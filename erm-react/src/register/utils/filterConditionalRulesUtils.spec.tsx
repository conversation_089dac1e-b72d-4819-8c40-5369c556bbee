import { ColumnType, FilterType } from '@protecht/ui-library/library/types';
import { getExpressionSymbol, getInvalidSelectedItemsForMSLibraryField, getViewExpressionValue } from './filterConditionalRulesUtils';

jest.mock('store', () => ({
    __esModule: true,
    default: {
        getState: jest.fn(() => ({})),
    },
}));

describe('getInvalidSelectedItemsForMSLibraryField', () => {
    it('should return empty array if no items are selected', () => {
        const selectedItems = [];
        const filterValue = 'test';

        const result = getInvalidSelectedItemsForMSLibraryField(selectedItems, filterValue, 'name', false);

        expect(result).toEqual([]);
    });

    it('should return invalid items when array field (e.g. tags) is not matching conditional filter', () => {
        const selectedItems = [{ tags: [{ name: 'some value', id: 1 }] }];
        const filterValue = 'test';

        const result = getInvalidSelectedItemsForMSLibraryField(selectedItems, filterValue, 'tags', false);

        expect(result).toHaveLength(1);
    });

    it('should return invalid items when object field (e.g. owner) is not matching conditional filter', () => {
        const selectedItems = [{ owner: { name: 'some value', id: 1 } }];
        const filterValue = 'test';

        const result = getInvalidSelectedItemsForMSLibraryField(selectedItems, filterValue, 'owner', false);

        expect(result).toHaveLength(1);
    });

    it('should return invalid items when basic field (e.g. name) is not matching conditional filter', () => {
        const selectedItems = [{ name: 'some value', id: 1 }];
        const filterValue = 'test';

        const result = getInvalidSelectedItemsForMSLibraryField(selectedItems, filterValue, 'name', false);

        expect(result).toHaveLength(1);
    });
});

describe('getViewExpressionValue', () => {
    it('should return undefined if field value is undefined', () => {
        expect(getViewExpressionValue(undefined, FilterType.STRING, ColumnType.SINGLELINE_TEXT)).toBeUndefined();
    });

    it('should return simple value for a primitive field', () => {
        expect(getViewExpressionValue('test', FilterType.STRING, ColumnType.SINGLELINE_TEXT)).toBe('test');
    });

    it('should return number value for a number field', () => {
        expect(getViewExpressionValue('5', FilterType.NUMBER, ColumnType.NUMERIC)).toBe(5);
    });

    it('should return parsed number for a currency field', () => {
        expect(getViewExpressionValue('$5', FilterType.NUMBER, ColumnType.CURRENCY)).toBe(5);
        expect(getViewExpressionValue('$1,234.56', FilterType.NUMBER, ColumnType.CURRENCY)).toBe(1234.56);
        expect(getViewExpressionValue('€-42.10', FilterType.NUMBER, ColumnType.CURRENCY)).toBe(-42.1);
    });

    it('should return first item of an array for array field', () => {
        const fieldValue = [{ name: 'tag1' }, { name: 'tag2' }];
        expect(getViewExpressionValue(fieldValue, FilterType.STRING, ColumnType.TABLE)).toEqual(fieldValue[0]);
    });
});

describe('getExpressionSymbol', () => {
    it('should return EQUAL for STRING type with exact match', () => {
        expect(getExpressionSymbol(FilterType.STRING, true)).toBe('=');
    });

    it('should return CONTAINS for STRING type without exact match', () => {
        expect(getExpressionSymbol(FilterType.STRING, false)).toBe('contains');
    });

    it('should return EQUAL for DATE type always', () => {
        expect(getExpressionSymbol(FilterType.DATE, true)).toBe('=');
        expect(getExpressionSymbol(FilterType.DATE, false)).toBe('=');
    });

    it('should return EQUAL for OBJECT type always', () => {
        expect(getExpressionSymbol(FilterType.OBJECT, true)).toBe('=');
        expect(getExpressionSymbol(FilterType.OBJECT, false)).toBe('=');
    });
});
