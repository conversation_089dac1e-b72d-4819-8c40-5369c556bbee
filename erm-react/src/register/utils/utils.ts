import { generatePath } from 'react-router';
import {
    ContractFieldType,
    EntryField,
    EntrySection,
    FieldStructure,
    Hyperlink,
    RegisterContractType,
    RegisterEntryRecord,
    RegisterEntryRest,
    RegisterRest,
    RegisterStructure,
    SectionMetaData,
    SectionStructure,
    StateStructure,
    StateTransitionStructure,
} from '../types';
import { AllEvaluatedRules, ConditionalRequired } from 'rules/types';
import { FilterType, ObjectStatus, SectionFieldMetaData } from '@protecht/ui-library/library/types';
import { EMPTY_OPTION_VALUE, EMPTY_VALUE, NO_SELECTED_VALUE } from '@protecht/ui-library/library/constants';
import { ColumnType } from 'register/types';
import { RegisterPath } from 'register/routes';
import { IdOnly, IdWithName, IdWithNameAndStatusRest, SystemConfiguration } from 'app/types';
import { AUXILIARY_COLUMN_PATTERN, AUXILIARY_SUFFIX, CORE_SECTION_LABEL, MAIN_TAB_NAME, SKIP_COMPARE_VALUE } from '../constants';
import { LinkRequestResponse, LocalizedMessagesEnum, OperationalResilienceDefinition, ResourceResultRest } from 'resilience/types';
import { exists, strings } from 'common/utils/i18n';
import { InterpolateTerminologyFunc, SelectorType, SYSTEM_COLUMN } from 'common/types';
import { QuestionnaireEntryRest } from 'questionnaire/types';
import { getConstraint, getSelectorType } from '../components/RegisterField/utils';
import { createValidationSchema, ValidationType } from 'common/utils/yup.utils';
import { DateTime } from 'luxon';
import { END_OF_TODAY, PROTECHT_DATE_TIME_FORMAT, REGISTER_CONSTRAINTS_DATE_FORMAT, REGISTER_DATE_FORMAT, START_OF_TODAY } from 'common/constants';
import store from 'store';
import { getScaleSet } from 'app/selectors';
import { FieldRest, ProtechtUserRest, Register, RegisterDataRest, ScaleSetRest, TableMetadataRest } from 'api/generated/types';
import { getListItems, getListItemsForListRegisterField } from '../components/RegisterField/ListRegisterField/utils';
import { getEvaluationResult } from 'rules/utils';
import { Attachment } from '@protecht/ui-library/library/components/FileDropzone';
import { Dictionary, groupBy } from 'lodash';
import { selectEvaluatedRules } from 'rules/selectors';
import { VendorRiskManagementPath } from 'vendorRiskManagement/routes/vrmRoutes';
import { VendorPortalPath } from 'vendorRiskManagement/routes/vendorPortalRoutes';
import { getAttachmentUrl, getImageThumbnailUrl, getImageUrl } from 'common/utils/FileUtils';
import { LOOKUP_SUPPORTED_FIELDS } from 'common/components/SuggestionsInput/InputLookup';
import { getDataField } from 'common/utils/definitions';
import { getInvalidSelectedItemsForMSLibraryField, getMSLibraryFieldSelectorColDef } from './filterConditionalRulesUtils';

export const isCoreSection = (section: SectionMetaData) => section.label === CORE_SECTION_LABEL;

export const getCoreSection = (entry: RegisterEntryRest): EntrySection | undefined => {
    return entry?.sections?.find((section) => section.label === CORE_SECTION_LABEL);
};

export const getAllFieldMetaData = (register: RegisterRest, includeCoreSection = false, includeAuxiliary = false): SectionFieldMetaData[] => {
    const allFields = [] as SectionFieldMetaData[];

    register?.sections?.forEach((section) => {
        if (!includeCoreSection && section.label === CORE_SECTION_LABEL) {
            return;
        }

        let fields = section.fields;

        if (!includeAuxiliary) {
            fields = fields.filter((field) => !isAuxiliaryField(field.columnName));
        }

        allFields.push(...fields);
    });

    return allFields;
};

export const getSectionNameOfField = (register: RegisterRest, fieldId: number) => {
    return register.sections.find((section) => section.fields.find((sField) => sField.id === fieldId))?.label;
};

export const getCoreFields = (register: RegisterRest, filterArchivedFields?: boolean): SectionFieldMetaData[] => {
    if (!register) {
        return [];
    }

    const coreSection = register.sections.find((section) => section.label === CORE_SECTION_LABEL);

    if (filterArchivedFields) {
        return coreSection?.fields.filter((field) => !field.archived) ?? [];
    }

    return coreSection?.fields ?? [];
};

export const getAllAuxiliaryFields = (register: RegisterRest, filterArchivedFields?: boolean): SectionFieldMetaData[] => {
    if (!register) {
        return [];
    }

    const nonCoreSections = register.sections.filter((section) => section.label !== CORE_SECTION_LABEL) ?? [];
    const fields = nonCoreSections.reduce((fields, section) => {
        const sectionFields = section.fields.filter((field) => isAuxiliaryField(field.columnName));
        return [...fields, ...sectionFields];
    }, []);

    if (filterArchivedFields) {
        return fields.filter((field) => !field.archived);
    }

    return fields;
};

export const getAllNonAuxiliaryFields = (register: RegisterRest, filterArchivedFields?: boolean): SectionFieldMetaData[] => {
    if (!register) {
        return [];
    }

    const nonCoreSections = register.sections.filter((section) => section.label !== CORE_SECTION_LABEL) ?? [];
    const fields = nonCoreSections.reduce((fields, section) => {
        const sectionFields = section.fields.filter((field) => !isAuxiliaryField(field.columnName));
        return [...fields, ...sectionFields];
    }, []);

    if (filterArchivedFields) {
        return fields.filter((field) => !field.archived);
    }

    return fields;
};

/**
 * Get all fields from a register that match the specified column type
 * @param register The register to search in
 * @param columnType The column type to filter by
 * @param includeCoreSection Whether to include fields from core section (default: false)
 * @param includeAuxiliary Whether to include auxiliary fields (default: false)
 * @param filterArchivedFields Whether to filter out archived fields (default: false)
 * @returns Array of fields matching the specified column type
 */
export const getFieldsByColumnType = (
    register: TableMetadataRest,
    columnType: ColumnType,
    includeCoreSection = false,
    includeAuxiliary = false,
    includeArchivedFields = false,
): FieldRest[] => {
    if (!register) {
        return [];
    }

    const allFields: FieldRest[] = [];

    register.sections?.forEach((section) => {
        if (!includeCoreSection && section.label === CORE_SECTION_LABEL) {
            return;
        }

        let fields = section.fields ?? [];

        if (!includeAuxiliary) {
            fields = fields.filter((field) => !isAuxiliaryField(field.columnName));
        }

        if (!includeArchivedFields) {
            fields = fields.filter((field) => !field.archived);
        }

        const matchingFields = fields.filter((field) => field.columnType === columnType);
        allFields.push(...matchingFields);
    });

    return allFields;
};

export const getFieldFromRegisterByColumnName = (register: RegisterRest, columnName: string): SectionFieldMetaData | null => {
    for (const section of register.sections) {
        const field = getRegisterField(section, columnName);
        if (field) {
            return field;
        }
    }
    return null;
};

export const getRegisterField = (section: SectionMetaData, columnName: string): SectionFieldMetaData | undefined => {
    return section?.fields?.find((f) => f.columnName === columnName);
};

export const getEntryField = (section: EntrySection, columnName: string): EntryField | undefined => {
    return section?.fields?.find((f) => f.fieldName === columnName);
};

/**
 * Find entry field in any register entry base on fieldName
 * @param entry Register Entry
 * @param columnName
 */
export const getRegisterEntryField = (entry?: RegisterEntryRest, columnName?: string): EntryField | undefined => {
    for (const section of entry?.sections || []) {
        const entryField = getEntryField(section, columnName || SYSTEM_COLUMN.ID);
        if (entryField) {
            return entryField;
        }
    }
};

/**
 * Get simple value from given register entry's field.
 * @param registerEntryRecord register entry record
 * @param columnName column name
 * @param defaultValue default value if not found
 */
export const getRegisterEntryFieldSimpleValue = (
    registerEntryRecord: RegisterEntryRecord,
    columnName?: string | null,
    defaultValue = '',
): string | string[] | undefined => {
    if (!columnName) {
        return defaultValue;
    }
    const entryField = getRegisterEntryField(registerEntryRecord.record, columnName);
    return getFieldValue(entryField, defaultValue);
};

export const getRegisterFieldLabel = (register: RegisterRest, columnName: string) => {
    const field = getFieldFromRegisterByColumnName(register, columnName);
    return field?.label;
};

export const getQuestionnaireMetadataEntryRow = (entry: RegisterDataRest): IdOnly => {
    const data: IdOnly = {
        id: entry.record!.id!,
    };

    data['completed'] = entry.completed;
    data['editableByCurrentUser'] = entry.editableByCurrentUser;

    return data;
};

export const getEntryTableRow = (
    entry: RegisterEntryRest | QuestionnaireEntryRest,
    register?: RegisterRest | null,
    additionalEntryFields?: EntryField[],
): IdOnly => {
    const data: IdOnly = {
        id: entry.id,
    };

    if (register?.stateful && entry.status) {
        data[register.statusColumn.columnName] = entry.status;
    }

    entry?.sections?.forEach((section) => {
        section?.fields?.forEach((field) => {
            data[field.fieldName] = field;
        });
    });
    additionalEntryFields?.forEach((field) => {
        data[field.fieldName] = field;
    });

    return data;
};

export const isAuxiliaryField = (columnName?: string): boolean => {
    return !!columnName?.match(AUXILIARY_COLUMN_PATTERN);
};

export const getColumnAuxiliaryFields = (field?: SectionFieldMetaData, auxiliaryFields?: SectionFieldMetaData[]): SectionFieldMetaData[] => {
    if (!field || !auxiliaryFields) {
        return [];
    }

    const filteredAuxFields = auxiliaryFields.filter((auxField) => auxField.columnName.includes(field.columnName));
    return filteredAuxFields;
};

export const getAuxiliaryFieldsByColumnName = (columnName: string, entry?: RegisterEntryRecord): EntryField[] => {
    return (
        entry?.record.sections?.reduce(
            (acc: EntryField[], section) =>
                acc.concat(section.fields.filter((field) => isAuxiliaryField(field.fieldName) && field.fieldName.includes(columnName))),
            [],
        ) ?? []
    );
};

export const getMainColumnNameForAuxiliaryField = (field: SectionFieldMetaData): string => {
    return field.columnName.substring(0, field.columnName.indexOf(AUXILIARY_SUFFIX));
};

export const isAnonymousRegisterEntry = (pathname: string): boolean => {
    return pathname.includes('/anonymous');
};

export const isCreatingNewRegisterEntry = (pathname: string): boolean => {
    return pathname.includes('/create');
};

/**
 * Create array of LinkRequestResponse instances as a request to find identifiers
 * of all entries of given register that have a reference to at least one resource
 * entry from given resources.
 * @param register register (which entry identifiers to find)
 * @param resources resources to find entry identifiers for
 * @param configuration resilience definition (containing all resource registers)
 */
export const createLinkRequest = (
    register: RegisterRest | null | undefined,
    resources: ResourceResultRest[],
    configuration: OperationalResilienceDefinition | null,
) => {
    // prepare link request
    const request: LinkRequestResponse[] = [];
    // prepare regex pattern
    const pattern = /^(table_[0-9]*)_(col_[0-9]*)_(table_[0-9]*)$/;
    // iterate over fields
    configuration &&
        register?.sections.forEach((section) => {
            section?.fields
                // filter fields representing resources
                .filter((field) => isResourceField(field, configuration))
                .forEach((field) => {
                    // get resource table name
                    const linkingTableName = field.constraintProperties['linking-table-name']?.value;
                    const match = pattern.exec(linkingTableName);
                    if (match) {
                        const resourceTableName = match[3];
                        // get resource identifiers for given resource table name
                        const resourceIds = getResourceIds(resources, resourceTableName);
                        if (register.tableName && field.columnName && resourceIds.length > 0) {
                            request.push({
                                table: register.tableName,
                                column: field.columnName,
                                ids: resourceIds,
                            });
                        }
                    }
                });
        });
    return request;
};

/**
 * Check whether given field is a table field linked to a resource register.
 * @param field field to check
 * @param configuration resilience definition containing all relevant resource registers
 * @return true if given field represents link to resource register, false otherwise
 */
const isResourceField = (field: SectionFieldMetaData, configuration: OperationalResilienceDefinition): boolean => {
    if (
        field &&
        (field.columnType === ColumnType.TABLE || field.columnType === ColumnType.ACTIONS) &&
        field.constraintProperties['table-id']?.value &&
        field.constraintProperties['linking-table-name']?.value
    ) {
        for (const resource of configuration.resourcesRegisters) {
            if (resource.registerId == field.constraintProperties['table-id']?.value) {
                return true;
            }
        }
    }
    return false;
};

/**
 * Get all resource identifiers from given resources for given resource table.
 * @param resources resources to get identifiers from
 * @param resourceTableName resource table name to get identifiers for
 */
const getResourceIds = (resources: ResourceResultRest[], resourceTableName: string): number[] => {
    const resourceIds: number[] = [];
    resources
        .filter((resource) => resource.type === resourceTableName)
        .forEach((resource) => {
            resource.info.forEach((info) => {
                if (resourceIds.indexOf(info.resId) === -1) {
                    resourceIds.push(info.resId);
                }
            });
        });
    return resourceIds;
};

/**
 * Checks if provided register contains table field with provided ID
 * @param register to be checked
 * @param tableId ID of the referencing table
 * @returns true if register contains such table field, false otherwise
 */
const hasSubtable = (register: RegisterRest, tableId: number): boolean => {
    for (const section of register.sections) {
        const found = section?.fields?.find(
            (field) => field.columnType === ColumnType.TABLE && field.constraintProperties?.['table-id']?.value === `${tableId}`,
        );
        if (found) {
            return true;
        }
    }

    return false;
};

/**
 * critical Service Map Register is mandatory
 */
export const validateCriticalServiceMapRegister = (interpolateString: InterpolateTerminologyFunc, criticalServiceMapRegister?: RegisterRest): void => {
    if (!criticalServiceMapRegister) {
        throw strings('ermMessages:mandatory_field', {
            0: interpolateString('resilience:label.criticalServicesRegister', [
                { placeholderString: 'service', terminologyItem: LocalizedMessagesEnum.RESILIENCE_SERVICE },
            ]),
        });
    }
};

/**
 * Process Register is valid if selected Critical Service Register has subtable field referencing the Process Register
 */
export const validateProcessRegister = (
    interpolateString: InterpolateTerminologyFunc,
    criticalServiceMapRegister?: RegisterRest,
    processRegister?: RegisterRest,
): void => {
    if (!processRegister) {
        throw strings('ermMessages:mandatory_field', {
            0: interpolateString('resilience:title.serviceProcess', [
                { placeholderString: 'service', terminologyItem: LocalizedMessagesEnum.RESILIENCE_SERVICE },
                { placeholderString: 'processes', terminologyItem: LocalizedMessagesEnum.RESILIENCE_PROCESSES },
            ]),
        });
    }

    if (criticalServiceMapRegister && !hasSubtable(criticalServiceMapRegister, processRegister?.id)) {
        throw interpolateString('resilience:message.processRegisterInvalid', [
            { placeholderString: 'service', terminologyItem: LocalizedMessagesEnum.RESILIENCE_SERVICE },
            { placeholderString: 'process', terminologyItem: LocalizedMessagesEnum.RESILIENCE_PROCESS },
        ]);
    }
};

/**
 * Plausible Scenario Register is valid if it has subtable field referencing one of the selected Resource Register
 */
export const validatePlausibleScenarioRegister = (
    interpolateString: InterpolateTerminologyFunc,
    plausibleScenariosRegister?: RegisterRest,
    resourcesRegisterIds?: number[],
): void => {
    if (!plausibleScenariosRegister) {
        throw strings('ermMessages:mandatory_field', {
            0: interpolateString('resilience:title.plausibleScenarios', [
                { placeholderString: 'plausibleScenarios', terminologyItem: LocalizedMessagesEnum.RESILIENCE_PLAUSIBLE_SCENARIOS },
            ]),
        });
    }

    if (
        resourcesRegisterIds &&
        resourcesRegisterIds.length > 0 &&
        !resourcesRegisterIds.find((resourceRegisterId) => hasSubtable(plausibleScenariosRegister, resourceRegisterId))
    ) {
        throw interpolateString('resilience:message.plausibleScenariosRegisterInvalid', [
            { placeholderString: 'resource', terminologyItem: LocalizedMessagesEnum.RESILIENCE_OPERATIONAL_RESOURCE },
            { placeholderString: 'plausibleScenarios', terminologyItem: LocalizedMessagesEnum.RESILIENCE_PLAUSIBLE_SCENARIOS },
        ]);
    }
};

/**
 * Resource Register is valid if selected Process Register has subtable field referencing the Resource Register
 */
export const validateResourceRegister = (interpolateString: InterpolateTerminologyFunc, processRegister: RegisterRest, resourceRegisterId: number): void => {
    if (!hasSubtable(processRegister, resourceRegisterId)) {
        throw interpolateString('resilience:message.resourceRegisterInvalid', [
            { placeholderString: 'resource', terminologyItem: LocalizedMessagesEnum.RESILIENCE_OPERATIONAL_RESOURCE },
            { placeholderString: 'process', terminologyItem: LocalizedMessagesEnum.RESILIENCE_PROCESS },
        ]);
    }
};

// TODO: should be used in Risk Matrix field as well
export const getConsequenceLabel = (scaleSet?: ScaleSetRest, constraintProperties?: unknown) => {
    const scaleType = constraintProperties?.['scale-type']?.value;
    const consequence = scaleSet?.consequences?.find((si) => `${si.id}` === `${scaleType}`);

    return constraintProperties?.['label-consequence']?.value || consequence?.name || strings('register:labels.consequence');
};

export const isVrmModule = () => {
    return window.location.pathname.includes(VendorRiskManagementPath.VRM) || window.location.pathname.includes(VendorPortalPath.VENDOR_PORTAL);
};

export const getFieldLabel = (field: SectionFieldMetaData, section?: SectionMetaData, parentField?: SectionFieldMetaData): string => {
    if (parentField && isAuxiliaryField(field.columnName) && field.columnType === ColumnType.SCALE_CONSEQUENCE) {
        const scaleSet = getScaleSet(store.getState(), parentField.constraintProperties?.['scale-set']?.value);

        const consequenceLabel = getConsequenceLabel(scaleSet, parentField.constraintProperties);
        return parentField.label + ' - ' + consequenceLabel;
    }

    if (isVrmModule() && section?.label === CORE_SECTION_LABEL && field.columnType === ColumnType.BUSINESS_UNIT) {
        return strings('vrm:label.vendorName');
    }

    return field.fieldLabel || field.label;
};

export type ValidationParams = unknown;

export type ValidationData = {
    validationType: ValidationType;
    validationTypeError?: string;
    validations: Map<string, ValidationParams | ValidationParams[]>;
};

const getFieldValidations = (
    field: SectionFieldMetaData,
    rules: AllEvaluatedRules | null,
    registerId: number,
    registerStructure: RegisterStructure,
    sectionReadOnly: boolean,
): ValidationData => {
    const sectionId = (registerStructure[field.columnName] as FieldStructure)?.section ?? '';
    const tabId = sectionId ? (registerStructure[sectionId] as SectionStructure)?.tab ?? MAIN_TAB_NAME : MAIN_TAB_NAME;

    const fieldRules = getEvaluationResult(rules, `${sectionId}-fields`, field.columnName);
    // default validations
    const validationData: ValidationData = {
        validationType: 'string',
        validations: new Map<string, ValidationParams | ValidationParams[]>(),
    };

    const sectionRules = getEvaluationResult(rules, `${tabId}-sections`, sectionId);
    const sectionVisible = sectionRules?.visible ?? true;
    const sectionEditable = sectionRules?.editable ?? true;
    const sectionRequired = sectionRules?.required ?? ConditionalRequired.UNSET;

    const tabRules = getEvaluationResult(rules, 'tabs', tabId);
    const tabVisible = tabRules?.visible ?? true;
    const tabEditable = tabRules?.editable ?? true;
    const tabRequired = tabRules?.required ?? ConditionalRequired.UNSET;

    if (
        field.archived ||
        fieldRules?.visible === false ||
        sectionVisible === false ||
        tabVisible === false ||
        fieldRules?.editable === false ||
        sectionEditable === false ||
        tabEditable === false ||
        sectionReadOnly === false
    ) {
        validationData.validationType = 'mixed';
        return validationData;
    }

    // skip validation for unsupported MSL fields for now
    if (field.columnType === ColumnType.MULTISELECT_LIBRARY) {
        const selectorType = getSelectorType(field?.constraintProperties);
        if (
            !selectorType ||
            (selectorType &&
                ![
                    SelectorType.BUSINESS_UNIT,
                    SelectorType.USER,
                    SelectorType.RISK_CAUSE,
                    SelectorType.RISK_EVENT,
                    SelectorType.CONTROL,
                    SelectorType.KRI,
                    SelectorType.AUDIT_QUESTION,
                    SelectorType.BOWTIE,
                    SelectorType.QUESTION,
                ].includes(selectorType))
        ) {
            validationData.validationType = 'array';
            return validationData;
        }
    }

    switch (field.columnType) {
        case ColumnType.BOOLEAN: {
            validationData.validationType = 'boolean';
            break;
        }
        case ColumnType.EMAIL: {
            validationData.validationType = 'string';
            validationData.validations.set('email', null);
            break;
        }
        case ColumnType.CURRENCY: {
            validationData.validationType = 'number';

            const max = getConstraint(field.constraintProperties, 'numeric_max');
            const min = getConstraint(field.constraintProperties, 'numeric_min');

            if (typeof max !== 'undefined') {
                validationData.validations.set('max', max);
            }
            if (typeof min !== 'undefined') {
                validationData.validations.set('min', min);
            }

            break;
        }
        case ColumnType.INTEGER:
        case ColumnType.NUMERIC: {
            validationData.validationType = 'number';

            if (field.columnType === ColumnType.INTEGER) {
                validationData.validations.set('integer', null);
            }

            const max = getConstraint(field.constraintProperties, 'numeric_max');
            const min = getConstraint(field.constraintProperties, 'numeric_min');

            if (typeof max !== 'undefined') {
                validationData.validations.set('max', max);
            }
            if (typeof min !== 'undefined') {
                validationData.validations.set('min', min);
            }

            break;
        }
        case ColumnType.SINGLELINE_TEXT:
        case ColumnType.MULTILINE_TEXT: {
            const maxLength = getConstraint(field.constraintProperties, 'character_length');
            if (maxLength) {
                validationData.validations.set('max', maxLength);
            }
            break;
        }
        case ColumnType.DUE_DATE:
        case ColumnType.DATE:
        case ColumnType.TIMESTAMP:
        case ColumnType.TIMESTAMP_WITH_TIMEZONE: {
            validationData.validationType = 'date';
            const minDate = getConstraint(field.constraintProperties, 'date_min');
            const maxDate = getConstraint(field.constraintProperties, 'date_max');

            if (minDate) {
                validationData.validations.set('min', DateTime.fromFormat(minDate, REGISTER_CONSTRAINTS_DATE_FORMAT));
            }
            if (maxDate) {
                const maxValue = DateTime.fromFormat(maxDate, REGISTER_CONSTRAINTS_DATE_FORMAT).endOf('day');
                validationData.validations.set('max', maxValue);
            }

            const disableFuture = getConstraint(field?.constraintProperties, 'date_in_past') === 'true';
            const disablePast = getConstraint(field?.constraintProperties, 'date_in_future') === 'true';
            const allowCurrentDate = getConstraint(field?.constraintProperties, 'allow-current-date') === 'true';

            if (disableFuture) {
                const today = END_OF_TODAY;
                const maxAllowedDate = today.minus({ days: allowCurrentDate ? 0 : 1 });
                validationData.validations.set('max', maxAllowedDate);
            }

            if (disablePast) {
                const today = START_OF_TODAY;
                const minAllowedDate = today.plus({ days: allowCurrentDate ? 0 : 1 });
                validationData.validations.set('min', minAllowedDate);
            }

            break;
        }
        case ColumnType.LIST: {
            const otherValueEnabled = getConstraint(field?.constraintProperties, 'otherValueEnabled') === 'true';

            if (otherValueEnabled) {
                const options = getListItemsForListRegisterField(field);
                const lastOption = options.at(-1);
                setFieldValidationAsArray(validationData.validations, 'test', [
                    'required-additional-input',
                    strings('common:validators.requiredSimple'),
                    (value, ctx) => {
                        const isFromInput = ctx.options.context?.[field.columnName]?.validationData?.isFromInput;

                        if (isFromInput) {
                            return value !== EMPTY_VALUE && value !== lastOption?.value;
                        }

                        return value !== lastOption?.value;
                    },
                ]);
            }

            const isDropdownSelect = getConstraint(field?.constraintProperties, 'style') === 'ListFieldDropdown';
            if (isDropdownSelect) {
                setFieldValidationAsArray(validationData.validations, 'test', [
                    'filterConditionalRule',
                    strings('common:validators.invalidSelectedOption', { value: '${originalValue}' }),
                    (value: string) => {
                        const filteredOptions =
                            (selectEvaluatedRules(store.getState(), registerId, `${sectionId}-fields`, field.columnName)?.filter?.filterValue as string[]) ??
                            [];
                        return !filteredOptions || filteredOptions.length === 0
                            ? true
                            : filteredOptions.some((option) => option === value) || value === NO_SELECTED_VALUE;
                    },
                ]);
            }

            break;
        }

        case ColumnType.GPS_POSITION:
        case ColumnType.CUSTOM_ID:
        case ColumnType.SIGN_OFF: {
            validationData.validationType = 'mixed';
            break;
        }

        case ColumnType.USER: {
            validationData.validationType = 'array';
            setFieldValidationAsArray(validationData.validations, 'test', [
                'filterConditionalRule',
                strings('common:validators.invalidSelectedItem'),
                ([selectedItem], ctx) => {
                    const filter = selectEvaluatedRules(store.getState(), registerId, `${sectionId}-fields`, field.columnName)?.filter;
                    const { filteredField, sourceFilterByField } = filter ?? {};

                    // currently only validating by id and name is supported todo add other cases
                    if (filter && filteredField && sourceFilterByField && ['id', 'name'].includes(filteredField) && selectedItem) {
                        const isExactMatch = filter.isExactMatch;
                        const sourceFilterByFieldValue = ctx.from[0].value[sourceFilterByField] ?? '';
                        const filterValue = (Array.isArray(sourceFilterByFieldValue) ? sourceFilterByFieldValue[0] : sourceFilterByFieldValue)
                            .toString()
                            .toLowerCase();

                        const userFieldValue = selectedItem[filteredField].toString().toLowerCase();
                        return isExactMatch ? userFieldValue === filterValue : userFieldValue.includes(filterValue);
                    } else {
                        return true;
                    }
                },
            ]);
            break;
        }

        case ColumnType.BUSINESS_UNIT: {
            validationData.validationType = 'array';
            setFieldValidationAsArray(validationData.validations, 'test', [
                'filterConditionalRule',
                strings('common:validators.invalidSelectedItem'),
                (selectedBU, ctx) => {
                    const selectedBUId = selectedBU[0]?.id.toString();
                    const validBUIds = ctx.options.context?.[field.columnName].validationData?.map((v) => v.toString());

                    //todo check if exact match is needed here
                    if (validBUIds && validBUIds.length > 0 && selectedBUId) {
                        return validBUIds.includes(selectedBUId);
                    } else {
                        return true;
                    }
                },
            ]);
            break;
        }

        case ColumnType.MULTISELECT_LIBRARY: {
            validationData.validationType = 'array';
            setFieldValidationAsArray(validationData.validations, 'test', [
                'filterConditionalRule',
                strings('common:validators.invalidSelectedItem'),
                (selected: IdWithName[], ctx) => {
                    const selectorType = getSelectorType(field?.constraintProperties);

                    if (
                        selectorType &&
                        [
                            SelectorType.RISK_CAUSE,
                            SelectorType.RISK_EVENT,
                            SelectorType.CONTROL,
                            SelectorType.KRI,
                            SelectorType.AUDIT_QUESTION,
                            SelectorType.BOWTIE,
                            SelectorType.QUESTION,
                        ].includes(selectorType)
                    ) {
                        const filter = selectEvaluatedRules(store.getState(), registerId, `${sectionId}-fields`, field.columnName)?.filter;
                        const { filteredField: apiField, sourceFilterByField } = filter ?? {};
                        const colDef = getMSLibraryFieldSelectorColDef(selectorType);
                        let filteredField = apiField ? getDataField(colDef!, apiField) : undefined;

                        // hotfix: incorrect CR field from BE on risk control with default frequency as filteredField
                        if (filteredField === 'default frequency') {
                            filteredField = 'defaultFrequency';
                        }

                        if (
                            filter &&
                            filteredField &&
                            sourceFilterByField &&
                            ['name', 'description', 'tags', 'owner', 'riskCauses', 'riskEvents', 'defaultFrequency', 'kriCategory', 'question'].includes(
                                filteredField,
                            ) &&
                            selected.length > 0
                        ) {
                            const isExactMatch = filter.isExactMatch;
                            const sourceFilterByFieldValue = ctx.from[0].value[sourceFilterByField] ?? '';
                            const filterValue = Array.isArray(sourceFilterByFieldValue)
                                ? sourceFilterByFieldValue[0]?.[filteredField]
                                : sourceFilterByFieldValue;

                            const invalidItems = getInvalidSelectedItemsForMSLibraryField(selected, filterValue, filteredField, isExactMatch);

                            const isTableView = getConstraint(field.constraintProperties, 'table-view') === 'true';

                            return invalidItems.length === 0
                                ? true
                                : ctx.createError({
                                      message: strings('common:validators.someInvalidSelectedItems', {
                                          items: invalidItems.map((item) => `- ${isTableView ? item.id : item.name}`).join('\n'),
                                      }),
                                  });
                        } else {
                            return true;
                        }
                    } else if (selectorType === SelectorType.BUSINESS_UNIT) {
                        const validBUIds = ctx.options.context[field.columnName].validationData?.map((v) => v.toString());

                        if (Array.isArray(validBUIds) && selected.length > 0) {
                            return selected.every((selectedItem) => validBUIds.includes(selectedItem.id.toString()));
                        } else {
                            return true;
                        }
                    } else if (selectorType === SelectorType.USER) {
                        const filter = selectEvaluatedRules(store.getState(), registerId, `${sectionId}-fields`, field.columnName)?.filter;
                        const { filteredField, sourceFilterByField } = filter ?? {};

                        if (filter && filteredField && sourceFilterByField && ['id', 'name'].includes(filteredField) && selected.length > 0) {
                            const isExactMatch = filter.isExactMatch;
                            const sourceFilterByFieldValue = ctx.from[0].value[sourceFilterByField] ?? '';
                            const filterValue = Array.isArray(sourceFilterByFieldValue)
                                ? sourceFilterByFieldValue[0]?.[filteredField]
                                : sourceFilterByFieldValue;

                            return isExactMatch
                                ? selected.every(
                                      (selectedItem) => selectedItem[filteredField].toString().toLowerCase() === filterValue.toString().toLowerCase(),
                                  )
                                : selected.every((selectedItem) =>
                                      selectedItem[filteredField].toString().toLowerCase().includes(filterValue.toString().toLowerCase()),
                                  );
                        } else {
                            return true;
                        }
                    } else {
                        return true;
                    }
                },
            ]);
            break;
        }
        case ColumnType.LINKED_TO: {
            const relatedLinks = getConstraint(field?.constraintProperties, 'property-allow-related') === 'true';
            if (relatedLinks) {
                validationData.validationType = 'array';
            }
            break;
        }
        // todo improve displaying of invalid items after design requirements are ready
        case ColumnType.TABLE:
        case ColumnType.ACTIONS:
        case ColumnType.CENTRAL_LIBRARY: {
            validationData.validationType = 'array';
            setFieldValidationAsArray(validationData.validations, 'test', [
                'filterConditionalRule',
                strings('common:validators.invalidSelectedItem'),
                (_, ctx) => {
                    const invalidItems = ctx.options.context[field.columnName].validationData;
                    if (invalidItems && invalidItems.length > 0) {
                        return ctx.createError({
                            message: strings('common:validators.someInvalidSelectedItems', {
                                items: invalidItems.map((item) => item.id).join(', '),
                            }),
                        });
                    }
                    return true;
                },
            ]);

            break;
        }
        case ColumnType.CONTROL:
        case ColumnType.RISK_CAUSE:
        case ColumnType.RISK_EVENT: {
            validationData.validationType = 'array';
            setFieldValidationAsArray(validationData.validations, 'test', [
                'filterConditionalRule',
                strings('common:validators.invalidSelectedItem'),
                ([selectedItem], ctx) => {
                    const filter = selectEvaluatedRules(store.getState(), registerId, `${sectionId}-fields`, field.columnName)?.filter;
                    const { filteredField, sourceFilterByField } = filter ?? {};
                    if (
                        filter &&
                        filteredField &&
                        sourceFilterByField &&
                        ['id', 'name', 'tags', 'description', 'defaultFrequency', 'riskEvents', 'owner', 'riskCause'].includes(filteredField) &&
                        selectedItem?.[0]
                    ) {
                        const isExactMatch = filter.isExactMatch;
                        const sourceFilterByFieldValue = ctx.from[0].value[sourceFilterByField] ?? '';
                        const filterValue = (Array.isArray(sourceFilterByFieldValue) ? sourceFilterByFieldValue[0] : sourceFilterByFieldValue)
                            .toString()
                            .toLowerCase();

                        const fieldValue = selectedItem[filteredField].toString().toLowerCase();
                        return isExactMatch ? fieldValue === filterValue : fieldValue.includes(filterValue);
                    } else {
                        return true;
                    }
                },
            ]);
            break;
        }

        case ColumnType.ATTACHMENT:
        case ColumnType.IMAGES:
        case ColumnType.MULTISELECT_LIST:
        case ColumnType.COUNTRY:
        case ColumnType.STATE:
        case ColumnType.TAGS:
        case ColumnType.ROLE:
        case ColumnType.HYPERLINK: {
            validationData.validationType = 'array';
            break;
        }
        default: {
            break;
        }
    }

    if (LOOKUP_SUPPORTED_FIELDS.includes(field.columnType)) {
        setFieldValidationAsArray(validationData.validations, 'test', [
            'noMatch',
            strings('common:validators.noSelections'),
            (value, ctx) => {
                const context = ctx.options.context?.[field.columnName]?.fieldData;

                if (context) {
                    return context.searchResults === 'noMatch' && (!value || value.length > 0);
                } else {
                    return true;
                }
            },
        ]);
    }

    if (fieldRules?.required === ConditionalRequired.NOT_REQUIRED) {
        return validationData;
    }

    if (
        fieldRules?.required === ConditionalRequired.REQUIRED ||
        sectionRequired === ConditionalRequired.REQUIRED ||
        tabRequired === ConditionalRequired.REQUIRED
    ) {
        if (validationData.validationType === 'array') {
            setRequiredValidationForArrayField(validationData.validations, field);
        } else {
            validationData.validations.set('required', null);
        }
    }

    if (sectionRequired === ConditionalRequired.NOT_REQUIRED || tabRequired === ConditionalRequired.NOT_REQUIRED) {
        return validationData;
    }

    if (field.required) {
        if (validationData.validationType === 'array') {
            setRequiredValidationForArrayField(validationData.validations, field);
        } else {
            validationData.validations.set('required', null);
        }
    }

    return validationData;
};

const setRequiredValidationForArrayField = (validations: Map<string, ValidationParams | ValidationParams[]>, field: SectionFieldMetaData) => {
    if (LOOKUP_SUPPORTED_FIELDS.includes(field.columnType)) {
        setFieldValidationAsArray(validations, 'test', [
            'required',
            strings('common:validators.requiredSimple'),
            (value, ctx) => {
                const searchResults = ctx.options.context?.[field.columnName]?.fieldData?.searchResults;

                if (!searchResults) {
                    return !value || value.length > 0;
                } else {
                    return true;
                }
            },
        ]);
    } else {
        validations.set('test', [
            'required',
            strings('common:validators.requiredSimple'),
            (value) => {
                return !value || value.length > 0;
            },
        ]);
    }
};

const setFieldValidationAsArray = (map: Map<string, ValidationParams | ValidationParams[]>, key: string, value: unknown) => {
    if (Array.isArray(map.get(key))) {
        (map.get(key) as ValidationParams[]).push(value);
    } else {
        map.set(key, [value]);
    }
};

export const createEntryValidationSchema = (register: RegisterRest, rules: AllEvaluatedRules | null, registerStructure: RegisterStructure) => {
    const fields = register.sections.reduce<any>((a, section) => {
        return a.concat(
            section.fields.map((field) => {
                return {
                    controlName: field.columnName,
                    controlLabel: getFieldLabel(field, section),
                    validationData: getFieldValidations(field, rules, register.id, registerStructure, section.editable),
                    columnType: field.columnType,
                };
            }),
        );
    }, []);

    return createValidationSchema(fields);
};

export const getFieldValue = (field?: EntryField, defaultValue = ''): string | string[] => {
    if (field?.simpleValue && Array.isArray(field.simpleValue) && field.simpleValue.length === 1) {
        return field.simpleValue[0];
    } else if (field?.simpleValue && Array.isArray(field.simpleValue) && field.simpleValue.length > 1) {
        // e.g. tag field can have multiple values inside the array and we need all of them
        return field.simpleValue;
    } else {
        return defaultValue;
    }
};

type FieldValue = string | string[] | Attachment[] | boolean | IdWithName[] | IdWithName | Hyperlink[] | google.maps.LatLngLiteral;
export const entryDataToFormData = (
    isCreateForm: boolean,
    register: RegisterRest | null,
    data: RegisterEntryRecord | null,
    currentUser: ProtechtUserRest,
    systemConfiguration: SystemConfiguration,
): Record<string, FieldValue> | undefined => {
    if (!register || !data) {
        return undefined;
    }

    const fields = data.record.sections.reduce<[string, FieldValue][]>((acc, section) => {
        return acc.concat(
            section.fields.map((field) => {
                const sectionMetadata = register.sections.find((s) => s.label === section.label);
                const fieldMetadata = sectionMetadata && getRegisterField(sectionMetadata, field.fieldName);
                let defaultValue = isCreateForm ? getConstraint(fieldMetadata?.constraintProperties, 'default-value') : '';

                if (isCreateForm && fieldMetadata?.columnType && [ColumnType.DATE, ColumnType.DUE_DATE].indexOf(fieldMetadata.columnType) !== -1) {
                    defaultValue =
                        getConstraint(fieldMetadata?.constraintProperties, 'default-to-now') === 'true' ? START_OF_TODAY.toFormat(REGISTER_DATE_FORMAT) : '';
                }

                if (
                    isCreateForm &&
                    fieldMetadata?.columnType &&
                    [ColumnType.TIMESTAMP, ColumnType.TIMESTAMP_WITH_TIMEZONE].indexOf(fieldMetadata.columnType) !== -1
                ) {
                    defaultValue =
                        getConstraint(fieldMetadata?.constraintProperties, 'default-to-now') === 'true'
                            ? DateTime.now().toFormat(PROTECHT_DATE_TIME_FORMAT)
                            : '';
                }

                if (isCreateForm && fieldMetadata?.columnType === ColumnType.LIST) {
                    const options = getListItems(fieldMetadata);
                    const defaultOption = options.find((option) => option.default);
                    if (defaultOption) {
                        defaultValue = defaultOption.value === EMPTY_VALUE ? EMPTY_OPTION_VALUE : defaultOption.value;
                    }
                } else if (fieldMetadata?.columnType === ColumnType.LIST) {
                    const value = field?.simpleValue?.[0];

                    const hasAdditionalValue = getConstraint(fieldMetadata?.constraintProperties, 'otherValueEnabled') === 'true';
                    if (hasAdditionalValue) {
                        const transformedValue = value === EMPTY_VALUE ? EMPTY_OPTION_VALUE : value ?? NO_SELECTED_VALUE;
                        return [field.fieldName, transformedValue];
                    } else {
                        // check if value is still an available option
                        const options = getListItems(fieldMetadata);
                        const isOptionValid = options.some((option) => option.value === value);

                        const optionToSelect = isOptionValid ? value : undefined;
                        const transformedValue = optionToSelect === EMPTY_VALUE ? EMPTY_OPTION_VALUE : optionToSelect ?? NO_SELECTED_VALUE;
                        return [field.fieldName, transformedValue];
                    }
                }

                if (fieldMetadata?.columnType === ColumnType.ATTACHMENT || fieldMetadata?.columnType === ColumnType.IMAGES) {
                    const fileNames = field.parameters?.['fileNames'];
                    const fileSizesInBytes = field.parameters?.['fileSizesInBytes'];
                    const fileUuids = field.parameters?.['uuids'];
                    const createdByNames = field.parameters?.['createdByNames'];
                    const createdDateTimes = field.parameters?.['createdDateTimestamps'];

                    const attachments: Attachment[] =
                        fileNames?.map(
                            (name: string, index: number): Attachment => ({
                                name,
                                file: new File([`${name}`], name),
                                size: fileSizesInBytes[index],
                                uuid: fileUuids[index],
                                createdBy: createdByNames[index],
                                createdDateTime: DateTime.fromFormat(createdDateTimes[index], PROTECHT_DATE_TIME_FORMAT),

                                ...(fieldMetadata?.columnType === ColumnType.IMAGES
                                    ? {
                                          url: getImageUrl(fileUuids[index]),
                                          thumbnailUrl: getImageThumbnailUrl(fileUuids[index]),
                                      }
                                    : {
                                          url: getAttachmentUrl(fileUuids[index], systemConfiguration.open_attachments_in_browser === 'true'),
                                      }),
                            }),
                        ) ?? [];

                    return [field.fieldName, attachments];
                }

                if (fieldMetadata?.columnType === ColumnType.MULTISELECT_LIST) {
                    return [field.fieldName, field?.simpleValue ?? []];
                }

                if (
                    fieldMetadata?.columnType === ColumnType.TABLE ||
                    fieldMetadata?.columnType === ColumnType.ACTIONS ||
                    fieldMetadata?.columnType === ColumnType.CENTRAL_LIBRARY
                ) {
                    const names = field.parameters?.displayValues ?? [];
                    const statuses = field.parameters?.statuses ?? [];
                    return [
                        field.fieldName,
                        field.simpleValue?.map((id, index) => ({
                            id: parseInt(id),
                            name: names[index] && names[index] !== 'null' ? names[index] : id,
                            status: statuses[index] ? parseInt(statuses[index]) : undefined,
                        })) ?? [],
                    ];
                }

                if (fieldMetadata?.columnType === ColumnType.BUSINESS_UNIT) {
                    // set to primary business unit when:
                    // - a new entry is being created
                    // - and current user info is available
                    // - and the given option is checked in the register designer
                    if (isCreateForm && currentUser.businessUnit?.id && getConstraint(fieldMetadata?.constraintProperties, 'defaultToPrimaryBU') === 'true') {
                        const primaryBU: IdWithNameAndStatusRest = {
                            id: currentUser.businessUnit.id,
                            name: currentUser.businessUnit.name ?? currentUser.businessUnit.id.toString(),
                            status: ObjectStatus.Active,
                        };
                        return [field.fieldName, [primaryBU]];
                    } else {
                        // when some business unit is already selected
                        if (field.simpleValue?.[0]) {
                            const selectedBusinessUnit: IdWithNameAndStatusRest = {
                                id: parseInt(field.simpleValue[0]),
                                name: field.parameters?.displayValues?.[0] ?? field.simpleValue[0],
                                status: field?.parameters?.statuses?.[0] ? parseInt(field?.parameters?.statuses?.[0]) : undefined,
                            };
                            return [field.fieldName, [selectedBusinessUnit]];
                        } else {
                            // empty array when no selected business unit
                            return [field.fieldName, []];
                        }
                    }
                }

                if (fieldMetadata?.columnType === ColumnType.SIGN_OFF) {
                    if (field.simpleValue?.[0]) {
                        const userId = field.simpleValue[0];
                        const user: IdWithName = {
                            id: parseInt(userId),
                            name: field.parameters?.userName?.[0] ?? userId,
                        };
                        return [field.fieldName, user];
                    } else {
                        return [field.fieldName, ''];
                    }
                }

                if (fieldMetadata?.columnType === ColumnType.USER) {
                    // set to currently logged user when:
                    // - a new entry is being created
                    // - and current user info is available
                    // - and the given option is checked in the register designer
                    if (isCreateForm && currentUser.id && getConstraint(fieldMetadata?.constraintProperties, 'default-logged-user') === 'true') {
                        const currentUserItem: IdWithNameAndStatusRest = {
                            id: currentUser.id,
                            name: currentUser.name ?? currentUser.id.toString(),
                            status: currentUser.status,
                        };
                        return [field.fieldName, [currentUserItem]];
                    } else {
                        // when some user is already selected
                        if (field.simpleValue?.[0]) {
                            const selectedUser: IdWithNameAndStatusRest = {
                                id: parseInt(field.simpleValue[0]),
                                name: field.parameters?.displayValues?.[0] ?? field.simpleValue[0],
                                status: field?.parameters?.statuses?.[0] ? parseInt(field?.parameters?.statuses?.[0]) : undefined,
                            };
                            return [field.fieldName, [selectedUser]];
                        } else {
                            // empty array when no selected user
                            return [field.fieldName, []];
                        }
                    }
                }

                if (fieldMetadata?.columnType === ColumnType.ROLE) {
                    if (field.simpleValue?.[0]) {
                        const selectedRole: IdWithNameAndStatusRest = {
                            id: parseInt(field.simpleValue[0]),
                            name: field.parameters?.displayValues?.[0] ?? field.simpleValue[0],
                            status: field?.parameters?.statuses?.[0] ? parseInt(field?.parameters?.statuses?.[0]) : undefined,
                        };
                        return [field.fieldName, [selectedRole]];
                    } else {
                        return [field.fieldName, []];
                    }
                }

                if (fieldMetadata?.columnType === ColumnType.MULTISELECT_LIBRARY) {
                    if (field.simpleValue?.[0]) {
                        const preselectedValues = field?.simpleValue?.map((value, i) => {
                            return {
                                id: parseInt(value),
                                name: field?.parameters?.displayValues?.[i] ?? '',
                                status: field?.parameters?.statuses?.[i] ? parseInt(field?.parameters?.statuses?.[i]) : undefined,
                            };
                        });
                        return [field.fieldName, preselectedValues];
                    } else {
                        return [field.fieldName, []];
                    }
                }

                if (fieldMetadata?.columnType === ColumnType.COUNTRY || fieldMetadata?.columnType === ColumnType.STATE) {
                    if (field.simpleValue?.[0]) {
                        const defaultValue: IdWithName = {
                            id: parseInt(field.simpleValue[0]),
                            name: field.parameters?.displayValues?.[0] || '',
                        };
                        return [field.fieldName, [defaultValue]];
                    } else {
                        return [field.fieldName, []];
                    }
                }

                if (fieldMetadata?.columnType === ColumnType.BOOLEAN) {
                    return [field.fieldName, field?.simpleValue?.[0] === 'true'];
                }

                if (fieldMetadata?.columnType === ColumnType.HYPERLINK) {
                    return [field.fieldName, field.simpleValue?.[0] ? JSON.parse(field.simpleValue?.[0]) : []];
                }

                if (fieldMetadata?.columnType === ColumnType.WORKLOG) {
                    // work log will be loaded by additional request
                    return [field.fieldName, ''];
                }

                if (isRelatedLinksField(field.fieldName, register)) {
                    // related links will be loaded by additional request
                    // using empty array as initial value to compare if new links were added or removed later
                    return [field.fieldName, [EMPTY_VALUE]];
                }

                if (fieldMetadata?.columnType === ColumnType.GPS_POSITION) {
                    const parsedValue = field?.simpleValue?.[0] ? field?.simpleValue?.[0].split(',') : [];
                    return [field.fieldName, field?.simpleValue?.[0] ? { lat: parseFloat(parsedValue[0]), lng: parseFloat(parsedValue[1]) } : ''];
                }

                if (fieldMetadata?.columnType === ColumnType.SLIDER) {
                    return [field.fieldName, field?.simpleValue?.[0] ? Number(field?.simpleValue?.[0]) : ''];
                }

                if (fieldMetadata?.columnType === ColumnType.TAGS) {
                    return [field.fieldName, field.simpleValue ?? []];
                }

                if (
                    fieldMetadata?.columnType === ColumnType.CONTROL ||
                    fieldMetadata?.columnType === ColumnType.RISK_CAUSE ||
                    fieldMetadata?.columnType === ColumnType.RISK_EVENT
                ) {
                    const id = field?.simpleValue?.[0];
                    const name = field?.parameters?.displayValues?.[0] ?? '';
                    const status = field?.parameters?.statuses?.[0] ? parseInt(field?.parameters?.statuses?.[0]) : undefined;

                    return [field.fieldName, id ? [{ id: parseInt(id), name: name, status: status }] : []];
                }

                return [field.fieldName, getFieldValue(field, defaultValue)];
            }),
        );
    }, []);

    return Object.fromEntries(fields);
};

export const getContractColumn = (registerContractType: RegisterContractType, contractFieldType: ContractFieldType, register?: RegisterRest | null) => {
    if (!register) {
        return undefined;
    }

    const { contractMappings, sections } = register;

    const contract = contractMappings?.find((contract) => contract.type === registerContractType);

    if (!contract) {
        return undefined;
    }

    const mapping = contract.mappings?.find((mapping) => mapping.type === contractFieldType);

    if (!mapping) {
        return undefined;
    }

    return sections.flatMap((section) => section.fields).find((field) => field.columnName === mapping.column);
};

export const createFieldMetadata = (field: Partial<SectionFieldMetaData>) => {
    return {
        ...field,
    } as SectionFieldMetaData;
};

export const createSectiondMetadata = (section: Partial<SectionMetaData>) => {
    return {
        ...section,
    } as SectionMetaData;
};

export const getColumnTypeLabel = (columnType: ColumnType) => {
    const formattedColumnType = columnType
        ?.split('_')
        .map((word, index) => (index === 0 ? word.toLowerCase() : word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()))
        .join('');

    return exists(`register:columnTypeLabel.${formattedColumnType}`) ? strings(`register:columnTypeLabel.${formattedColumnType}`) : '';
};

export const isAttachmentField = (fieldName: string, register: RegisterRest): boolean => {
    return getFieldFromRegisterByColumnName(register, fieldName)?.columnType === ColumnType.ATTACHMENT;
};

export const isGalleryField = (fieldName: string, register: RegisterRest): boolean => {
    return getFieldFromRegisterByColumnName(register, fieldName)?.columnType === ColumnType.IMAGES;
};

export const isRelatedLinksField = (fieldName: string, register: RegisterRest): boolean => {
    const field = getFieldFromRegisterByColumnName(register, fieldName);
    return field?.columnType === ColumnType.LINKED_TO && getConstraint(field.constraintProperties, 'property-allow-related') === 'true';
};

export const getRegisterTabs = (register?: RegisterRest, isAnonymous?: boolean) => {
    if (register) {
        const sections = isAnonymous ? register.sections.filter((section) => !isCoreSection(section)) : register.sections;
        return groupBy(sections, (section: SectionMetaData) => {
            if (!register.tabLayout) {
                return MAIN_TAB_NAME;
            }
            return section.tabName || register.defaultTabName || MAIN_TAB_NAME;
        });
    }
};

export const getRegisterStructure = (register: RegisterRest, isAnonymous: boolean): RegisterStructure => {
    const tabs: Dictionary<SectionMetaData[]> | undefined = getRegisterTabs(register, isAnonymous);

    const registerStructure: RegisterStructure = {};

    if (!tabs) {
        return registerStructure;
    }

    Object.entries(tabs).forEach(([tabName, sections]) => {
        registerStructure[tabName] = { sections: sections.map((section) => section.id.toString()) };
        sections.forEach((section) => {
            registerStructure[section.id] = { tab: tabName, fields: section.fields.map((field) => field.columnName) };
            section.fields.forEach((field) => {
                registerStructure[field.columnName] = { tab: tabName, section: section.id.toString(), columnType: field.columnType };
            });
        });
    });

    // todo use stateTransitions and states when implementing register entry states as needed to enable conditional rules
    // todo check if permissions should limit available states and state transitions
    registerStructure.stateTransition = {} as StateTransitionStructure;
    if (register.stateDefinition?.stateTransitions?.length > 0) {
        register.stateDefinition?.stateTransitions.forEach((stateTransition) => {
            const transitionId = stateTransition.id.toString();
            registerStructure.stateTransition[transitionId] = stateTransition;
        });
    }

    registerStructure.state = {} as StateStructure;
    if (register.stateDefinition?.states?.length > 0) {
        register.stateDefinition?.states.forEach((state) => {
            const transitionId = state.id.toString();
            registerStructure.state[transitionId] = state;
        });
    }

    return registerStructure;
};

export const registerHasPermission = (permission: string, register?: TableMetadataRest) => {
    if (!register || !register.userPermissionCodesForRegister) {
        return false;
    }

    return register.userPermissionCodesForRegister.includes(permission);
};

export const permissionOf = (baseCode: string, application: string, objectIdentity: number, subIdentity: number | null = null) => {
    return `${baseCode}_${application}_${objectIdentity}${subIdentity === null ? '' : `_${subIdentity}`}`;
};

export const getRegisterEntryDisplayFieldValue = (register?: TableMetadataRest, entry?: RegisterDataRest) => {
    const columnName = register?.identityColumn?.columnName;
    const identityField = getRegisterEntryField(entry?.record as RegisterEntryRest, columnName);
    const identityFieldValue = getIdentityFieldValue(identityField);
    return identityFieldValue ?? `${entry?.record?.id}`;
};

export const getIdentityFieldValue = (identityField: EntryField | undefined) => {
    if (identityField?.simpleValue) {
        const displayValues = identityField.parameters?.displayValues || [];
        const valuesToDisplay = identityField.simpleValue.map((simpleValue, index) => {
            return displayValues[index] && displayValues[index] !== 'null' ? displayValues[index] : simpleValue;
        });
        return valuesToDisplay?.join(', ');
    }
};

export const getEmptyValue = (fieldName: string, registerStructure: RegisterStructure) => {
    const columnType = (registerStructure[fieldName] as FieldStructure)?.columnType;

    switch (columnType) {
        case ColumnType.BUSINESS_UNIT:
        case ColumnType.USER:
        case ColumnType.TAGS:
        case ColumnType.MULTISELECT_LIST:
        case ColumnType.MULTISELECT_LIBRARY:
        case ColumnType.TABLE:
        case ColumnType.ACTIONS:
        case ColumnType.STATE:
        case ColumnType.COUNTRY:
        case ColumnType.ATTACHMENT:
        case ColumnType.IMAGES:
        case ColumnType.HYPERLINK:
        case ColumnType.ROLE:
        case ColumnType.LINKED_TO:
        case ColumnType.CENTRAL_LIBRARY:
            return [];
        case ColumnType.BOOLEAN:
            return false;
        default:
            return '';
    }
};

const _getFilterTypeByColumnType = (columnType: ColumnType): FilterType | undefined => {
    switch (columnType) {
        case ColumnType.ATTACHMENT:
        case ColumnType.CURRENCY:
        case ColumnType.NUMERIC:
        case ColumnType.IMAGES:
        case ColumnType.REPORT_ATTACHMENT:
        case ColumnType.SLIDER:
        case ColumnType.INTEGER:
        case ColumnType.COMPLEX_FORMULA:
        case ColumnType.CUSTOM_ID:
        case ColumnType.SIMPLE_FORMULA:
            return FilterType.NUMBER;
        case ColumnType.BOOLEAN:
            return FilterType.BOOLEAN;
        case ColumnType.DUE_DATE:
        case ColumnType.DATE:
        case ColumnType.TIMESTAMP:
        case ColumnType.TIMESTAMP_WITH_TIMEZONE:
        case ColumnType.DATETIME_FORMULA:
            return FilterType.DATE;
        case ColumnType.ACTIONS:
        case ColumnType.LINKED_TO:
        case ColumnType.SINGLELINE_TEXT:
        case ColumnType.EMAIL:
        case ColumnType.GPS_POSITION:
        case ColumnType.HINT:
        case ColumnType.HYPERLINK:
        case ColumnType.LIST:
        case ColumnType.MULTILINE_TEXT:
        case ColumnType.MULTISELECT_LIST:
        case ColumnType.RICH_TEXT:
        case ColumnType.STATIC_TEXT:
        case ColumnType.WORKLOG:
        case ColumnType.BUSINESS_UNIT:
        case ColumnType.ROLE:
        case ColumnType.COUNTRY:
        case ColumnType.SIGN_OFF:
        case ColumnType.STATE:
        case ColumnType.USER:
        case ColumnType.STRING_FORMULA:
        case ColumnType.SCALE_CONSEQUENCE:
        case ColumnType.CONTROL_ATTESTATION:
        case ColumnType.SCALE_LIKELIHOOD:
        case ColumnType.RISK_RATING:
        case ColumnType.RISK_MATRIX:
        case ColumnType.CONTROL:
        case ColumnType.RISK_CAUSE:
        case ColumnType.RISK_EVENT:
        case ColumnType.SPACER:
        case ColumnType.FRAMEWORK_LINKS:
        case ColumnType.MULTISELECT_LIBRARY:
        case ColumnType.QUESTION:
        case ColumnType.TAGS:
        case ColumnType.TABLE:
        case ColumnType.CENTRAL_LIBRARY:
        case ColumnType.USER_INFO:
            return FilterType.STRING;
        default:
            return undefined;
    }
};

// TODO: remove and use util function from UI library utils/date.ts
export const formatDateFieldValue = (date: Date | string, format: string): string => {
    if (typeof date === 'string') {
        return DateTime.fromJSDate(new Date(date)).toFormat(format);
    }

    return DateTime.fromJSDate(date).toFormat(format);
};


export function mapValueToSimpleValue(value: any): string[] {
    if (Array.isArray(value)) {
        if (value.length === 0) {
            return [];
        } else if (typeof value[0] === 'object' && value[0]?.id !== undefined) {
            return value.map((item) => item.id.toString());
        } else if (typeof value[0] === 'string' || typeof value[0] === 'number') {
            return value.map((item) => item.toString());
        } else {
            return [];
        }
    } else if (typeof value === 'string' || typeof value === 'number') {
        return [value.toString()];
    } else if (typeof value === 'object' && value !== null && value.id !== undefined) {
        return [value.id.toString()];
    } else {
        return [];
    }
}

export function updateEntryRecord(record?: Register, formValues?: Record<string, any>): Register {
    if (!record || !formValues) {
        return record!;
    }

    const updatedRecord = JSON.parse(JSON.stringify(record));

    const fieldMap = new Map<string, any>();
    for (const section of updatedRecord.sections) {
        for (const field of section.fields) {
            fieldMap.set(field.fieldName, field);
        }
    }

    for (const key in formValues) {
        if (Object.prototype.hasOwnProperty.call(formValues, key)) {
            const value = formValues[key];

            const field = fieldMap.get(key);
            if (field) {
                field.simpleValue = mapValueToSimpleValue(value);
            }
        }
    }

    return updatedRecord;
}

export const registerHasUserFieldWithMessaging = (register?: TableMetadataRest) => {
    if (!register) {
        return false;
    }
    const userFields = getFieldsByColumnType(register, ColumnType.USER);
    const userFieldsWithMessaging = userFields.filter((field) => getConstraint(field?.constraintProperties, 'messaging') === 'true');
    return userFieldsWithMessaging.length > 0;
};

export const registerHasAttachmentField = (register?: TableMetadataRest) => {
    if (!register) {
        return false;
    }
    const attachmentFields = getFieldsByColumnType(register, ColumnType.ATTACHMENT);
    const galleryFields = getFieldsByColumnType(register, ColumnType.IMAGES);
    return attachmentFields.length > 0 || galleryFields.length > 0;
};

export const registerHasFormulaField = (register?: TableMetadataRest) => {
    if (!register) {
        return false;
    }
    const simpleFormulaFields = getFieldsByColumnType(register, ColumnType.SIMPLE_FORMULA);
    const dateTimeFormulaFields = getFieldsByColumnType(register, ColumnType.DATETIME_FORMULA);
    const stringFormulaFields = getFieldsByColumnType(register, ColumnType.STRING_FORMULA);
    const complexFormulaFields = getFieldsByColumnType(register, ColumnType.COMPLEX_FORMULA);
    return simpleFormulaFields.length > 0 || dateTimeFormulaFields.length > 0 || stringFormulaFields.length > 0 || complexFormulaFields.length > 0;
};

export const registerHasDueDateField = (register?: TableMetadataRest) => {
    if (!register) {
        return false;
    }

    const dueDateFields = getFieldsByColumnType(register, ColumnType.DUE_DATE);

    return dueDateFields.length > 0;
};

export const getEntryBreadcrumbs = (register: TableMetadataRest) => {
    return [
        {
            label: register.applicationName!,
            pathname: generatePath(RegisterPath.REGISTER_LIST, {
                appId: register.applicationId!.toString(),
            }),
        },
        {
            label: register.label!,
            pathname: generatePath(RegisterPath.REGISTER_LIST_REGISTER, {
                appId: register.applicationId!.toString(),
                tableName: register.tableName!,
            }),
        },
    ];
};

const isDefaultToNow = (f: SectionFieldMetaData): boolean =>
  [ColumnType.DATE, ColumnType.DUE_DATE, ColumnType.TIMESTAMP, ColumnType.TIMESTAMP_WITH_TIMEZONE]
    .includes(f.columnType as any) &&
  getConstraint(f.constraintProperties, 'default-to-now') === 'true';

const calcExpectedDefault = (f: SectionFieldMetaData): string[] => {
  if (isDefaultToNow(f)) {
    return [SKIP_COMPARE_VALUE];
  }

  if (f.columnType === ColumnType.LIST) {
    const listItems = getListItems(f);
    const opt = listItems.find(o => o.default);
    if (opt) {
        const result = [opt.value === EMPTY_VALUE ? EMPTY_OPTION_VALUE : opt.value];
        return result;
    }
  }

  const dv = getConstraint(f.constraintProperties, 'default-value');
  return dv ? [dv.toString()] : [];
};

const hasAnyDefaultInRegister = (register: RegisterRest): boolean =>
  register?.sections
    ?.filter(sec => !isCoreSection(sec))
    ?.some(sec => sec.fields?.some(f => calcExpectedDefault(f).length)) ?? false;

export const areAllFieldsAtDefaultValues = (
  register: RegisterRest,
  entry:    RegisterEntryRecord
): boolean =>
  register?.sections
    ?.filter(sec => !isCoreSection(sec))
    ?.every(section =>
      section.fields?.every(fm => {
        const expected = calcExpectedDefault(fm);
        if (expected[0] === SKIP_COMPARE_VALUE) {
          return true;
        }

        const actual =
          entry?.record?.sections
               ?.find(s => s.label === section.label)
               ?.fields?.find(f => f.fieldName === fm.columnName)?.simpleValue ?? [];

        const result = JSON.stringify(actual) === JSON.stringify(expected);
        return result;
      }) ?? true
    ) ?? true;

export const shouldFormBeDirtyBasedOnDefaults = (
  register: RegisterRest | null,
  entry:    RegisterEntryRecord | null
): boolean =>
  !!register &&
  !!entry &&
  !entry.completed &&
  hasAnyDefaultInRegister(register) &&
  areAllFieldsAtDefaultValues(register, entry);
