import { resetLayoutProps } from 'app/reducer';
import { systemApi } from 'app/rtkApi';
import { getCurrentRoute } from 'app/selectors';
import React from 'react';
import { RouteObject } from 'react-router';

import { RegisterConfirmation, RegisterEntryListLayout, RegisterList, GuidedEntryPage } from 'register/components';
import { loadApplication } from 'register/reducer';
import store from 'store';
import { usersApi } from 'user/rtkApi';
import { viewsApi } from 'view/rtkApi';
import { registerApi } from 'register/rtkApi';
import RegisterEntryPage from './components/RegisterEntryPage';

const applicationLoader = async ({ params }) => {
    if (params.appId) {
        const appId = Number(params.appId);
        void store.dispatch(loadApplication({ appId }));
    }
    return null;
};

const entryListLoader = async ({ params }) => {
    if (params.tableName) {
        void store.dispatch(registerApi.endpoints.tmrsGetRegisterConfigUsingGet.initiate({ tableName: params.tableName }, { subscribe: false }));

        const previousPathname = getCurrentRoute(store.getState()).to;

        // TODO: improve pattern matching
        if (!previousPathname.includes('/entries/')) {
            store.dispatch(resetLayoutProps());
        }
    }
    return null;
};

const RegisterRoutes: RouteObject[] = [
    {
        path: 'registers',
        loader: () => {
            void store.dispatch(systemApi.endpoints.srsGetScaleSetsUsingGet.initiate({ pagingDisabled: true }, { subscribe: false }));
            void store.dispatch(usersApi.endpoints.pursGetUserPermissionsUsingGet.initiate({}, { subscribe: false }));
            void store.dispatch(viewsApi.endpoints.vrsGetExpressionContextUsingGet.initiate(undefined, { subscribe: false }));

            return null;
        },
        children: [
            {
                path: ':tableName/entries',
                element: <RegisterEntryListLayout />,
                loader: entryListLoader,
                children: [
                    { path: ':entryId', element: <RegisterEntryPage /> },
                    {
                        path: 'anonymous',
                        element: <RegisterEntryPage isAnonymous={true} />,
                    },
                ],
            },
            {
                path: ':tableName/entries/anonymous/confirmation',
                element: <RegisterConfirmation />,
            },
            {
              path: ':tableName/gentry',
                element: <GuidedEntryPage />,
            },
        ],
    },
    {
        path: 'app',
        loader: () => {
            void store.dispatch(systemApi.endpoints.srsGetScaleSetsUsingGet.initiate({ pagingDisabled: true }, { subscribe: false }));
            void store.dispatch(usersApi.endpoints.pursGetUserPermissionsUsingGet.initiate({}, { subscribe: false }));
            void store.dispatch(viewsApi.endpoints.vrsGetExpressionContextUsingGet.initiate(undefined, { subscribe: false }));

            return null;
        },
        children: [
            {
                path: ':appId/registers',
                loader: applicationLoader,
                element: <RegisterList />,
                children: [
                    {
                        path: ':tableName/entries',
                        element: <RegisterList />,
                        loader: entryListLoader,
                        children: [
                            {
                                path: 'create',
                                element: (
                                    <RegisterEntryPage
                                        isCreate={true}
                                        isAnonymous={false}
                                    />
                                ),
                            },
                            { path: ':entryId', element: <RegisterEntryPage /> },
                            {
                                path: 'anonymous',
                                element: <RegisterEntryPage isAnonymous={true} />,
                            },
                            {
                                path: 'anonymous/confirmation',
                                element: <RegisterConfirmation />,
                            },
                        ],
                    },
                ],
            },
        ],
    },
];

export default RegisterRoutes;
