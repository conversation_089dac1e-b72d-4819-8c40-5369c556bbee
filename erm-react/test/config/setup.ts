import { ProtechtDictionary } from './ProtechtDictionaryMock';
import { ReactBridge } from './ReactBridgeMock';
import { GwtBridge } from './GwtBridgeMock';
import { ERM } from './ErmMock';
import { ThemeConfig } from './ThemeConfigMock';
import fetch from 'node-fetch';
import { Settings } from 'luxon';
import { LicenseInfo } from '@mui/x-data-grid-pro';
import { muiLicenseKey } from '../../src/config/muiLicenseKey';

import { TextEncoder } from 'util';

// IntersectionObserver isn't available in test environment
// more polyfills available by importing from https://github.com/Shopify/quilt/tree/main/packages/polyfills
// alternative import without shopify https://www.npmjs.com/package/intersection-observer
import '@shopify/polyfills/intersection-observer.jest';
import createGoogleMapsMock from 'common/components/GoogleMap/mocks';

LicenseInfo.setLicenseKey(muiLicenseKey);

(global as any).ProtechtDictionary = ProtechtDictionary;
(global as any).window.ReactBridge = ReactBridge;
(global as any).window.GwtBridge = GwtBridge;
(global as any).ERM = ERM;
(global as any).ThemeConfig = ThemeConfig;
(global as any).fetch = fetch;
(global as any).TextEncoder = TextEncoder;
(global as any).URL.createObjectURL = jest.fn();
(global as any).URL.revokeObjectURL = jest.fn();
(global as any).ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
}));

(global as any).MutationObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
    takeRecords: jest.fn(),
}));
export const mockPtgwtFunctionsWidget = jest.fn();
export const mockPtgwtFunctionsSetMenuContainerSizes = jest.fn();
export const mockPtgwtFunctionsSetContainerSizes = jest.fn();
(global as any).window.ptgwt = {
    Functions: {
        widget: mockPtgwtFunctionsWidget,
        setMenuContainerSizes: mockPtgwtFunctionsSetMenuContainerSizes,
        setContainerSizes: mockPtgwtFunctionsSetContainerSizes,
    },
};

(global as any).document.fonts = jest.fn().mockImplementation(() => ({
    status: 'loaded',
}));

(global as any).document.fonts.addEventListener = jest.fn();
(global as any).document.fonts.removeEventListener = jest.fn();

(global as any).google = { maps: createGoogleMapsMock() };

export const mockWalkMeApiIsMenuOpen = jest.fn();
export const mockWalkMeApiToggleMenu = jest.fn();
(global as any).WalkMePlayerAPI = {
    isMenuOpen: mockWalkMeApiIsMenuOpen,
    toggleMenu: mockWalkMeApiToggleMenu,
};

// it's always May 20
Settings.now = () => new Date(2018, 4, 20).valueOf();
